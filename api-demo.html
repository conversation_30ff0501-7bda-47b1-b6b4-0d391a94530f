<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QR Code Styling API - Versão API</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .api-status {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            font-size: 0.9rem;
        }

        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }

        .status-online { background: #00ff00; }
        .status-offline { background: #ff0000; }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .main-content {
            display: grid;
            grid-template-columns: 400px 1fr;
            gap: 30px;
            padding: 30px;
            min-height: calc(100vh - 200px);
        }

        .controls-panel {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            height: fit-content;
            max-height: calc(100vh - 160px);
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: #667eea #f8f9fa;
        }

        .controls-panel::-webkit-scrollbar {
            width: 6px;
        }

        .controls-panel::-webkit-scrollbar-track {
            background: #f8f9fa;
        }

        .controls-panel::-webkit-scrollbar-thumb {
            background: #667eea;
            border-radius: 3px;
        }

        .preview-panel {
            position: sticky;
            top: 30px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
            height: fit-content;
            max-height: calc(100vh - 160px);
            align-self: start;
        }

        .section {
            margin-bottom: 25px;
        }

        .section-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #667eea;
        }

        .input-group {
            margin-bottom: 15px;
        }

        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
        }

        .input-group input,
        .input-group select,
        .input-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .input-group input:focus,
        .input-group select:focus,
        .input-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }

        .input-group textarea {
            resize: vertical;
            min-height: 80px;
        }

        .color-input {
            width: 60px !important;
            height: 40px;
            padding: 0;
            border: none;
            border-radius: 8px;
            cursor: pointer;
        }

        .api-config {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .api-config input {
            font-family: monospace;
            font-size: 12px;
        }

        .endpoint-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 15px;
        }

        .endpoint-btn {
            padding: 12px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            font-size: 13px;
            color: white;
        }

        .endpoint-btn.generate {
            background: #4caf50;
        }

        .endpoint-btn.quick {
            background: #2196f3;
        }

        .endpoint-btn.download {
            background: #ff9800;
        }

        .endpoint-btn.batch {
            background: #9c27b0;
        }

        .endpoint-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .qr-preview {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            text-align: center;
            width: 100%;
            max-width: 500px;
        }

        .qr-container {
            border: 2px dashed #e0e0e0;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            width: 100%;
            max-width: 500px;
            min-height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            position: relative;
        }

        .qr-container img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }

        .qr-placeholder {
            color: #999;
            font-size: 1.1rem;
        }

        .response-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }

        .error-message {
            background: #ffe6e6;
            color: #d63031;
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
            display: none;
        }

        .success-message {
            background: #e6ffe6;
            color: #00b894;
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
            display: none;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .file-upload {
            position: relative;
            display: inline-block;
            width: 100%;
        }

        .file-upload input[type=file] {
            position: absolute;
            left: -9999px;
        }

        .file-upload-label {
            display: block;
            padding: 12px;
            background: #667eea;
            color: white;
            border-radius: 8px;
            cursor: pointer;
            text-align: center;
            transition: background 0.3s ease;
        }

        .file-upload-label:hover {
            background: #5a6fd8;
        }

        .request-details {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }

        .request-details summary {
            cursor: pointer;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .request-details pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 11px;
        }

        @media (max-width: 1024px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
                padding: 20px;
            }

            .controls-panel {
                order: 2;
                max-height: none;
                overflow-y: visible;
            }

            .preview-panel {
                order: 1;
                position: static;
                max-height: none;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 QR Code Styling - Versão API</h1>
            <p>Crie QR codes personalizados usando a API Backend ao invés do JavaScript</p>
            <div style="background: rgba(255,255,255,0.1); padding: 10px; border-radius: 8px; margin-top: 15px; font-size: 0.9rem;">
                <strong>🔧 Versão API:</strong> Todas as funcionalidades da biblioteca original, mas processadas no servidor backend!
            </div>
            <div class="api-status">
                <span id="api-status-indicator" class="status-indicator status-offline"></span>
                <span id="api-status-text">Verificando conexão com a API...</span>
            </div>
        </div>

        <div class="main-content">
            <div class="controls-panel">
                <!-- Configuração da API -->
                <div class="section">
                    <div class="section-title">🔧 Configuração da API</div>
                    <div class="api-config">
                        <div class="input-group">
                            <label for="api-base-url">URL Base da API:</label>
                            <input type="text" id="api-base-url" value="http://localhost:3000/api/v1" placeholder="http://localhost:3000/api/v1">
                        </div>
                        <button onclick="checkAPIStatus()" style="background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer;">
                            🔍 Verificar Status da API
                        </button>
                    </div>
                </div>

                <!-- Endpoints da API -->
                <div class="section">
                    <div class="section-title">🌐 Endpoints Disponíveis</div>
                    <div class="endpoint-buttons">
                        <button class="endpoint-btn generate" onclick="callGenerateAPI()">
                            🎨 Generate
                        </button>
                        <button class="endpoint-btn quick" onclick="callQuickAPI()">
                            ⚡ Quick
                        </button>
                        <button class="endpoint-btn download" onclick="callDownloadAPI()">
                            📥 Download
                        </button>
                        <button class="endpoint-btn batch" onclick="callBatchAPI()">
                            📦 Batch
                        </button>
                    </div>
                </div>

                <!-- Seção de Conteúdo -->
                <div class="section">
                    <div class="section-title">📝 Conteúdo</div>
                    <div class="input-group">
                        <label for="qr-text">Texto ou URL:</label>
                        <textarea id="qr-text" placeholder="Digite o texto que será convertido em QR Code...">https://github.com/kozakdenys/qr-code-styling</textarea>
                    </div>
                    <div style="margin-top: 10px;">
                        <button onclick="loadPixExample()" style="background: #32CD32; color: white; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer; font-size: 12px;">
                            📱 Testar com PIX
                        </button>
                        <button onclick="clearText()" style="background: #6c757d; color: white; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer; font-size: 12px; margin-left: 5px;">
                            🗑️ Limpar
                        </button>
                    </div>
                </div>
                <!-- Seção de Presets -->
                <div class="section">
                    <div class="section-title">🎨 Presets de Estilo</div>
                    <div class="preset-buttons" style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-top: 10px;">
                        <button class="endpoint-btn" style="background: #32CD32;" onclick="applyPreset('pix')">PIX Otimizado</button>
                        <button class="endpoint-btn" style="background: #2c3e50;" onclick="applyPreset('classic')">Clássico</button>
                        <button class="endpoint-btn" style="background: linear-gradient(45deg, #667eea, #764ba2);" onclick="applyPreset('modern')">Moderno</button>
                        <button class="endpoint-btn" style="background: linear-gradient(45deg, #ff6b6b, #ffd93d);" onclick="applyPreset('colorful')">Colorido</button>
                        <button class="endpoint-btn" style="background: #ecf0f1; color: #2c3e50; border: 2px solid #bdc3c7;" onclick="applyPreset('minimal')">Minimalista</button>
                        <button class="endpoint-btn" style="background: #34495e;" onclick="applyPreset('corporate')">Corporativo</button>
                    </div>
                </div>

                <!-- Seção de Configurações -->
                <div class="section">
                    <div class="section-title">⚙️ Configurações do QR Code</div>
                    <div class="input-group">
                        <label for="qr-width">Largura:</label>
                        <input type="number" id="qr-width" value="300" min="100" max="2000">
                    </div>
                    <div class="input-group">
                        <label for="qr-height">Altura:</label>
                        <input type="number" id="qr-height" value="300" min="100" max="2000">
                    </div>
                    <div class="input-group">
                        <label for="qr-margin">Margem:</label>
                        <input type="number" id="qr-margin" value="0" min="0" max="100">
                    </div>
                    <div class="input-group">
                        <label for="qr-shape">Formato do QR Code:</label>
                        <select id="qr-shape">
                            <option value="square" selected>Quadrado</option>
                            <option value="circle">Círculo</option>
                        </select>
                    </div>
                    <div class="input-group">
                        <label for="error-correction">Nível de Correção de Erro:</label>
                        <select id="error-correction">
                            <option value="L">Baixo (7%)</option>
                            <option value="M" selected>Médio (15%)</option>
                            <option value="Q">Alto (25%)</option>
                            <option value="H">Muito Alto (30%)</option>
                        </select>
                    </div>
                    <div class="input-group">
                        <label for="qr-format">Formato (preview e download):</label>
                        <select id="qr-format">
                            <option value="png" selected>PNG</option>
                            <option value="jpeg">JPEG</option>
                            <option value="webp">WebP</option>
                            <option value="svg">SVG</option>
                        </select>
                    </div>
                </div>

                <!-- Seção de Pontos -->
                <div class="section">
                    <div class="section-title">⚫ Configuração dos Pontos</div>
                    <div class="input-group">
                        <label for="dot-type">Tipo de Ponto:</label>
                        <select id="dot-type">
                            <option value="square">Quadrado</option>
                            <option value="rounded">Arredondado</option>
                            <option value="dots">Círculo</option>
                            <option value="classy">Elegante</option>
                            <option value="classy-rounded">Elegante Arredondado</option>
                            <option value="extra-rounded">Extra Arredondado</option>
                        </select>
                    </div>
                    <div class="input-group">
                        <label for="dot-color">Cor dos Pontos:</label>
                        <input type="color" id="dot-color" class="color-input" value="#000000">
                    </div>
                </div>

                <!-- Seção de Cantos -->
                <div class="section">
                    <div class="section-title">📐 Configuração dos Cantos</div>
                    <div class="input-group">
                        <label for="corner-square-type">Tipo de Canto (Quadrado):</label>
                        <select id="corner-square-type">
                            <option value="square">Quadrado</option>
                            <option value="extra-rounded">Extra Arredondado</option>
                            <option value="dot">Ponto</option>
                        </select>
                    </div>
                    <div class="input-group">
                        <label for="corner-dot-type">Tipo de Canto (Ponto Central):</label>
                        <select id="corner-dot-type">
                            <option value="square">Quadrado</option>
                            <option value="dot">Ponto</option>
                        </select>
                    </div>
                    <div class="input-group">
                        <label for="corner-square-color">Cor dos Cantos (Quadrado):</label>
                        <input type="color" id="corner-square-color" class="color-input" value="#000000">
                    </div>
                    <div class="input-group">
                        <label for="corner-dot-color">Cor dos Cantos (Ponto Central):</label>
                        <input type="color" id="corner-dot-color" class="color-input" value="#000000">
                    </div>
                </div>

                <!-- Seção de Cores e Gradientes -->
                <div class="section">
                    <div class="section-title">🎨 Cores e Gradientes</div>
                    <div class="input-group">
                        <label for="background-color">Cor de Fundo:</label>
                        <input type="color" id="background-color" class="color-input" value="#ffffff">
                    </div>
                    <div class="input-group">
                        <label>
                            <input type="checkbox" id="use-gradient"> Usar Gradiente nos Pontos
                        </label>
                    </div>
                    <div id="gradient-options" style="display: none;">
                        <div class="input-group">
                            <label for="gradient-color1">Cor 1 do Gradiente:</label>
                            <input type="color" id="gradient-color1" class="color-input" value="#667eea">
                        </div>
                        <div class="input-group">
                            <label for="gradient-color2">Cor 2 do Gradiente:</label>
                            <input type="color" id="gradient-color2" class="color-input" value="#764ba2">
                        </div>
                        <div class="input-group">
                            <label for="gradient-type">Tipo de Gradiente:</label>
                            <select id="gradient-type">
                                <option value="linear">Linear</option>
                                <option value="radial">Radial</option>
                            </select>
                        </div>
                        <div class="input-group">
                            <label for="gradient-rotation">Rotação (°):</label>
                            <input type="range" id="gradient-rotation" min="0" max="360" value="0">
                            <span id="gradient-rotation-value">0°</span>
                        </div>
                    </div>
                </div>

                <!-- Upload de Imagem -->
                <div class="section">
                    <div class="section-title">🖼️ Upload de Imagem</div>
                    <div class="input-group">
                        <div class="file-upload">
                            <input type="file" id="image-upload" accept="image/*" onchange="handleImageUpload(this)">
                            <label for="image-upload" class="file-upload-label">
                                📁 Selecionar Imagem
                            </label>
                        </div>
                        <img id="image-preview" class="image-preview" alt="Preview" style="margin-top:10px; max-width: 100px; max-height: 100px; display:none; border-radius: 8px;" />
                    </div>
                    <div class="input-group">
                        <label for="image-size">Tamanho da Imagem (%):</label>
                        <input type="range" id="image-size" min="10" max="50" value="20">
                        <span id="image-size-value">20%</span>
                    </div>
                </div>
            </div>

            <div class="preview-panel">
                <div class="qr-preview">
                    <h3>🎯 Resultado da API</h3>
                    <div id="qr-container" class="qr-container">
                        <div class="qr-placeholder">
                            Configure a API e clique em um dos endpoints para gerar o QR Code
                        </div>
                    </div>

                    <div class="response-info" id="response-info" style="display: none;"></div>

                    <details class="request-details">
                        <summary>📋 Detalhes da Requisição</summary>
                        <div>
                            <strong>Método:</strong> <span id="request-method">-</span><br>
                            <strong>URL:</strong> <span id="request-url">-</span><br>
                            <strong>Status:</strong> <span id="response-status">-</span><br>
                            <strong>Tempo:</strong> <span id="response-time">-</span>
                        </div>
                        <pre id="request-body"></pre>
                    </details>
                </div>

                <div class="error-message" id="error-message"></div>
                <div class="success-message" id="success-message"></div>
            </div>
        </div>
    </div>

    <script>
        let currentImageData = null;
        let apiBaseUrl = 'http://localhost:3000/api/v1';

        // Payload PIX de exemplo
        const EXAMPLE_PIX_PAYLOAD = '00020101021126630014br.gov.bcb.pix0111283047438410226Lorem ispum dolor sit amet52040000530398654041.625802BR5910Wellington6006Franca62120508PIXTESTE6304B181';

        // Função para mostrar mensagens
        function showMessage(message, type = 'success') {
            const errorEl = document.getElementById('error-message');
            const successEl = document.getElementById('success-message');

            errorEl.style.display = 'none';
            successEl.style.display = 'none';

            if (type === 'error') {
                errorEl.textContent = message;
                errorEl.style.display = 'block';
                setTimeout(() => errorEl.style.display = 'none', 5000);
            } else {
                successEl.textContent = message;
                successEl.style.display = 'block';
                setTimeout(() => successEl.style.display = 'none', 3000);
            }
        }

        // Função para carregar exemplo PIX
        function loadPixExample() {
            document.getElementById('qr-text').value = EXAMPLE_PIX_PAYLOAD;
            showMessage('🏦 Payload PIX de exemplo carregado!', 'success');
        }

        // Função para limpar texto
        function clearText() {
            document.getElementById('qr-text').value = '';
        }

        // Função para verificar status da API
        async function checkAPIStatus() {
            const statusIndicator = document.getElementById('api-status-indicator');
            const statusText = document.getElementById('api-status-text');

            apiBaseUrl = document.getElementById('api-base-url').value.trim();

            try {
                statusText.textContent = 'Verificando...';
                statusIndicator.className = 'status-indicator status-offline';

                const response = await fetch(`${apiBaseUrl.replace('/api/v1', '')}/health`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    statusIndicator.className = 'status-indicator status-online';
                    statusText.textContent = `✅ API Online - ${data.data.status} (v${data.data.version})`;
                    showMessage('✅ Conexão com a API estabelecida com sucesso!');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                statusIndicator.className = 'status-indicator status-offline';
                statusText.textContent = `❌ API Offline - ${error.message}`;
                showMessage('❌ Não foi possível conectar com a API. Verifique se o servidor está rodando.', 'error');
            }
        }

        // Função para atualizar detalhes da requisição
        function updateRequestDetails(method, url, status, time, body = null) {
            document.getElementById('request-method').textContent = method;
            document.getElementById('request-url').textContent = url;
            document.getElementById('response-status').textContent = status;
            document.getElementById('response-time').textContent = time + 'ms';
            document.getElementById('request-body').textContent = body ? JSON.stringify(body, null, 2) : 'Nenhum body';
        }

        // Função para criar opções do QR Code
        function createQROptions() {
            const options = {
                data: document.getElementById('qr-text').value.trim(),
                width: parseInt(document.getElementById('qr-width').value),
                height: parseInt(document.getElementById('qr-height').value),
                margin: parseInt(document.getElementById('qr-margin').value),
                dotsOptions: {
                    type: document.getElementById('dot-type').value,
                    color: document.getElementById('dot-color').value
                },
                backgroundOptions: {
                    color: document.getElementById('background-color').value
                },
                cornersSquareOptions: {
                    type: document.getElementById('corner-square-type').value,
                    color: document.getElementById('corner-square-color').value
                },
                cornersDotOptions: {
                    type: document.getElementById('corner-dot-type').value,
                    color: document.getElementById('corner-dot-color').value
                }
            };

            // Adicionar suporte a gradientes
            if (document.getElementById('use-gradient').checked) {
                const rotationDeg = parseInt(document.getElementById('gradient-rotation').value);
                const rotationRad = (rotationDeg * Math.PI) / 180;
                
                options.dotsOptions.gradient = {
                    type: document.getElementById('gradient-type').value,
                    rotation: rotationRad,
                    colorStops: [
                        { offset: 0, color: document.getElementById('gradient-color1').value },
                        { offset: 1, color: document.getElementById('gradient-color2').value }
                    ]
                };
                // Remover cor sólida quando usar gradiente
                delete options.dotsOptions.color;
            }

            if (currentImageData) {
                options.image = currentImageData;
                options.imageOptions = {
                    imageSize: 0.4,
                    hideBackgroundDots: true,
                    margin: 10
                };
            }

            return options;
        }

        // Função para chamar o endpoint /generate
        async function callGenerateAPI() {
            const options = createQROptions();

            if (!options.data) {
                showMessage('❌ Digite um texto para gerar o QR Code', 'error');
                return;
            }

            try {
                const startTime = Date.now();
                const url = `${apiBaseUrl}/qrcode/generate`;

                showMessage('🔄 Gerando QR Code via API...');

                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(options)
                });

                const endTime = Date.now();
                const responseTime = endTime - startTime;

                updateRequestDetails('POST', url, response.status, responseTime, options);

                if (response.ok) {
                    const result = await response.json();

                    // Mostrar informações da resposta
                    document.getElementById('response-info').style.display = 'block';
                    document.getElementById('response-info').textContent = JSON.stringify(result, null, 2);

                    // Tentar obter a imagem
                    if (result.success) {
                        showMessage('✅ QR Code gerado com sucesso via API!');

                        // Fazer uma nova requisição para obter o arquivo
                        const format = document.getElementById('qr-format').value;
                        await downloadAndDisplayQR(options, format);
                    }
                } else {
                    const error = await response.json();
                    document.getElementById('response-info').style.display = 'block';
                    document.getElementById('response-info').textContent = JSON.stringify(error, null, 2);
                    showMessage(`❌ Erro da API: ${error.message}`, 'error');
                }
            } catch (error) {
                console.error('Erro:', error);
                showMessage(`❌ Erro de conexão: ${error.message}`, 'error');
            }
        }

        // Função para chamar o endpoint /quick
        async function callQuickAPI() {
            const data = document.getElementById('qr-text').value.trim();

            if (!data) {
                showMessage('❌ Digite um texto para gerar o QR Code', 'error');
                return;
            }

            try {
                const startTime = Date.now();
                const size = document.getElementById('qr-width').value;
                const format = document.getElementById('qr-format').value;
                const color = encodeURIComponent(document.getElementById('dot-color').value);
                const background = encodeURIComponent(document.getElementById('background-color').value);
                const margin = document.getElementById('qr-margin').value;

                const url = `${apiBaseUrl}/qrcode/quick?data=${encodeURIComponent(data)}&size=${size}&format=${format}&color=${color}&background=${background}&margin=${margin}`;

                showMessage('⚡ Gerando QR Code rápido via API...');

                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Accept': `image/${format}`
                    }
                });

                const endTime = Date.now();
                const responseTime = endTime - startTime;

                updateRequestDetails('GET', url, response.status, responseTime);

                if (response.ok) {
                    const blob = await response.blob();
                    const imageUrl = URL.createObjectURL(blob);

                    // Mostrar a imagem
                    document.getElementById('qr-container').innerHTML = `<img src="${imageUrl}" alt="QR Code">`;

                    // Mostrar informações da resposta
                    document.getElementById('response-info').style.display = 'block';
                    document.getElementById('response-info').textContent = `Formato: ${format}\nTamanho: ${blob.size} bytes\nTipo: ${blob.type}`;

                    showMessage('✅ QR Code rápido gerado com sucesso!');
                } else {
                    const error = await response.json();
                    document.getElementById('response-info').style.display = 'block';
                    document.getElementById('response-info').textContent = JSON.stringify(error, null, 2);
                    showMessage(`❌ Erro da API: ${error.message}`, 'error');
                }
            } catch (error) {
                console.error('Erro:', error);
                showMessage(`❌ Erro de conexão: ${error.message}`, 'error');
            }
        }

        // Função para chamar o endpoint /download
        async function callDownloadAPI() {
            const options = createQROptions();

            if (!options.data) {
                showMessage('❌ Digite um texto para gerar o QR Code', 'error');
                return;
            }

            try {
                const startTime = Date.now();
                const format = document.getElementById('qr-format').value;
                const url = `${apiBaseUrl}/qrcode/download?format=${format}&filename=qr-demo`;

                showMessage('📥 Gerando download via API...');

                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(options)
                });

                const endTime = Date.now();
                const responseTime = endTime - startTime;

                updateRequestDetails('POST', url, response.status, responseTime, options);

                if (response.ok) {
                    const blob = await response.blob();

                    // Criar URL para download
                    const downloadUrl = URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = downloadUrl;
                    link.download = `qr-demo.${format}`;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    URL.revokeObjectURL(downloadUrl);

                    // Mostrar preview se for imagem
                    if (format !== 'svg') {
                        const imageUrl = URL.createObjectURL(blob);
                        document.getElementById('qr-container').innerHTML = `<img src="${imageUrl}" alt="QR Code">`;
                    } else {
                        const text = await blob.text();
                        document.getElementById('qr-container').innerHTML = text;
                    }

                    // Mostrar informações da resposta
                    document.getElementById('response-info').style.display = 'block';
                    document.getElementById('response-info').textContent = `Download realizado!\nFormato: ${format}\nTamanho: ${blob.size} bytes`;

                    showMessage('✅ Download realizado com sucesso!');
                } else {
                    const error = await response.json();
                    document.getElementById('response-info').style.display = 'block';
                    document.getElementById('response-info').textContent = JSON.stringify(error, null, 2);
                    showMessage(`❌ Erro da API: ${error.message}`, 'error');
                }
            } catch (error) {
                console.error('Erro:', error);
                showMessage(`❌ Erro de conexão: ${error.message}`, 'error');
            }
        }

        // Função para chamar o endpoint /batch
        async function callBatchAPI() {
            const baseOptions = createQROptions();

            // Criar um lote de exemplo
            const batchData = {
                commonOptions: {
                    width: baseOptions.width,
                    height: baseOptions.height,
                    dotsOptions: baseOptions.dotsOptions,
                    backgroundOptions: baseOptions.backgroundOptions
                },
                items: [
                    { data: 'Item 1 - ' + baseOptions.data },
                    { data: 'Item 2 - ' + baseOptions.data },
                    { data: 'Item 3 - ' + baseOptions.data }
                ]
            };

            try {
                const startTime = Date.now();
                const url = `${apiBaseUrl}/qrcode/batch`;

                showMessage('📦 Processando lote via API...');

                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(batchData)
                });

                const endTime = Date.now();
                const responseTime = endTime - startTime;

                updateRequestDetails('POST', url, response.status, responseTime, batchData);

                if (response.ok) {
                    const result = await response.json();

                    // Mostrar informações da resposta
                    document.getElementById('response-info').style.display = 'block';
                    document.getElementById('response-info').textContent = JSON.stringify(result, null, 2);

                    // Mostrar resumo no container
                    const summary = result.data.summary;
                    document.getElementById('qr-container').innerHTML = `
                        <div style="text-align: center;">
                            <h4>📦 Processamento em Lote</h4>
                            <p><strong>Total:</strong> ${summary.total}</p>
                            <p><strong>Sucessos:</strong> ${summary.successful}</p>
                            <p><strong>Falhas:</strong> ${summary.failed}</p>
                        </div>
                    `;

                    showMessage(`✅ Lote processado: ${summary.successful}/${summary.total} sucessos`);
                } else {
                    const error = await response.json();
                    document.getElementById('response-info').style.display = 'block';
                    document.getElementById('response-info').textContent = JSON.stringify(error, null, 2);
                    showMessage(`❌ Erro da API: ${error.message}`, 'error');
                }
            } catch (error) {
                console.error('Erro:', error);
                showMessage(`❌ Erro de conexão: ${error.message}`, 'error');
            }
        }

        // Função auxiliar para baixar e exibir QR Code
        async function downloadAndDisplayQR(options, format) {
            try {
                const url = `${apiBaseUrl}/qrcode/download?format=${format}`;

                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(options)
                });

                if (response.ok) {
                    const blob = await response.blob();

                    if (format === 'svg') {
                        const text = await blob.text();
                        document.getElementById('qr-container').innerHTML = text;
                    } else {
                        const imageUrl = URL.createObjectURL(blob);
                        document.getElementById('qr-container').innerHTML = `<img src="${imageUrl}" alt="QR Code">`;
                    }
                }
            } catch (error) {
                console.error('Erro ao obter imagem:', error);
            }
        }

        // Função para lidar com upload de imagem
        function handleImageUpload(input) {
            const file = input.files[0];
            if (!file) return;

            if (!file.type.startsWith('image/')) {
                showMessage('Por favor, selecione apenas arquivos de imagem.', 'error');
                return;
            }

            if (file.size > 5 * 1024 * 1024) {
                showMessage('A imagem deve ter no máximo 5MB.', 'error');
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                currentImageData = e.target.result;
                showMessage('Imagem carregada para uso na API!');
            };

            reader.onerror = function() {
                showMessage('Erro ao carregar a imagem.', 'error');
            };

            reader.readAsDataURL(file);
        }

        // Inicializar a página
        window.onload = function() {
            checkAPIStatus();
            showMessage('🚀 Demo da QR Code Styling API carregado! Configure a URL da API e teste os endpoints.');
        };

        // Atualizar URL da API quando mudou
        document.getElementById('api-base-url').addEventListener('change', function() {
            apiBaseUrl = this.value.trim();
        });
</script>

<!-- Live Preview Enhancements -->
<script>
    // ---------- Live Preview (debounce + loading + full options) ----------
    let __debounceTimer = null;
    let __abortController = null;
    let __lastObjectUrl = null;

    function isPixPayload(text) {
        return text.startsWith('00020101') && text.includes('br.gov.bcb.pix');
    }

    function getOptimizedErrorLevel(text) {
        const length = text.length;
        const pix = isPixPayload(text);
        if (pix || length > 150) return 'L';
        const sel = document.getElementById('error-correction');
        return sel ? sel.value : 'M';
    }

    function toggleGradient() {
        const useGradient = document.getElementById('use-gradient');
        const gradientOptions = document.getElementById('gradient-options');
        if (useGradient && gradientOptions) {
            gradientOptions.style.display = useGradient.checked ? 'block' : 'none';
        }
    }

    const __presets = {
        pix: { dotsOptions: { color: '#000000', type: 'square' }, backgroundOptions: { color: '#ffffff' }, cornersSquareOptions: { color: '#000000', type: 'square' }, cornersDotOptions: { color: '#000000', type: 'square' } },
        classic: { dotsOptions: { color: '#000000', type: 'square' }, backgroundOptions: { color: '#ffffff' }, cornersSquareOptions: { color: '#000000', type: 'square' }, cornersDotOptions: { color: '#000000', type: 'square' } },
        modern: { dotsOptions: { color: '#667eea', type: 'rounded' }, backgroundOptions: { color: '#f8f9fa' }, cornersSquareOptions: { color: '#764ba2', type: 'extra-rounded' }, cornersDotOptions: { color: '#667eea', type: 'dot' } },
        colorful: { dotsOptions: { type: 'dots' }, backgroundOptions: { color: '#fff3cd' }, cornersSquareOptions: { color: '#ffd93d', type: 'extra-rounded' }, cornersDotOptions: { color: '#ff6b6b', type: 'dot' } },
        minimal: { dotsOptions: { color: '#2c3e50', type: 'classy' }, backgroundOptions: { color: '#ffffff' }, cornersSquareOptions: { color: '#34495e', type: 'square' }, cornersDotOptions: { color: '#2c3e50', type: 'square' } },
        corporate: { dotsOptions: { color: '#1e3a8a', type: 'classy-rounded' }, backgroundOptions: { color: '#f1f5f9' }, cornersSquareOptions: { color: '#1e40af', type: 'extra-rounded' }, cornersDotOptions: { color: '#1e3a8a', type: 'dot' } }
    };

    function applyPreset(name) {
        const preset = __presets[name];
        if (!preset) return;
        if (preset.dotsOptions.color) document.getElementById('dot-color').value = preset.dotsOptions.color;
        if (preset.dotsOptions.type) document.getElementById('dot-type').value = preset.dotsOptions.type;
        if (preset.backgroundOptions?.color) document.getElementById('background-color').value = preset.backgroundOptions.color;
        if (preset.cornersSquareOptions) {
            document.getElementById('corner-square-color').value = preset.cornersSquareOptions.color;
            document.getElementById('corner-square-type').value = preset.cornersSquareOptions.type;
        }
        if (preset.cornersDotOptions) {
            document.getElementById('corner-dot-color').value = preset.cornersDotOptions.color;
            document.getElementById('corner-dot-type').value = preset.cornersDotOptions.type;
        }
        schedulePreviewUpdate();
    }

    function buildOptionsForPreview() {
        const data = document.getElementById('qr-text').value.trim();
        const userMargin = parseInt(document.getElementById('qr-margin').value);
        const pix = isPixPayload(data);
        const appliedMargin = pix && userMargin < 16 ? 16 : userMargin;
        const optimizedError = getOptimizedErrorLevel(data);
        const format = document.getElementById('qr-format').value;

        const opts = {
            data,
            width: parseInt(document.getElementById('qr-width').value),
            height: parseInt(document.getElementById('qr-height').value),
            type: format === 'svg' ? 'svg' : 'canvas',
            shape: document.getElementById('qr-shape').value,
            margin: appliedMargin,
            qrOptions: {
                typeNumber: 0,
                mode: pix ? 'Byte' : undefined,
                errorCorrectionLevel: optimizedError
            },
            dotsOptions: {
                type: document.getElementById('dot-type').value,
                roundSize: false
            },
            backgroundOptions: {
                color: document.getElementById('background-color').value
            },
            cornersSquareOptions: {
                color: document.getElementById('corner-square-color').value,
                type: document.getElementById('corner-square-type').value
            },
            cornersDotOptions: {
                color: document.getElementById('corner-dot-color').value,
                type: document.getElementById('corner-dot-type').value
            }
        };

        if (document.getElementById('use-gradient').checked) {
            const rotationDeg = parseInt(document.getElementById('gradient-rotation').value);
            document.getElementById('gradient-rotation-value').textContent = rotationDeg + '°';
            const rotationRad = (rotationDeg * Math.PI) / 180;
            opts.dotsOptions.gradient = {
                type: document.getElementById('gradient-type').value,
                rotation: rotationRad,
                colorStops: [
                    { offset: 0, color: document.getElementById('gradient-color1').value },
                    { offset: 1, color: document.getElementById('gradient-color2').value }
                ]
            };
        } else {
            opts.dotsOptions.color = document.getElementById('dot-color').value;
        }

        if (typeof currentImageData !== 'undefined' && currentImageData) {
            const imageSize = parseInt(document.getElementById('image-size').value);
            document.getElementById('image-size-value').textContent = imageSize + '%';
            opts.image = currentImageData;
            opts.imageOptions = { margin: 10, imageSize: imageSize / 100, hideBackgroundDots: true };
        }

        return opts;
    }

    function schedulePreviewUpdate() {
        clearTimeout(__debounceTimer);
        __debounceTimer = setTimeout(generateLivePreview, 300);
    }

    async function generateLivePreview() {
        const container = document.getElementById('qr-container');
        const format = document.getElementById('qr-format').value;
        const options = buildOptionsForPreview();

        if (!options.data) {
            container.innerHTML = '<div class="qr-placeholder">Digite um texto para gerar o QR Code</div>';
            return;
        }

        if (__abortController) { __abortController.abort(); }
        __abortController = new AbortController();
        const { signal } = __abortController;

        container.innerHTML = '<div class="loading" aria-label="Carregando"></div>';

        const startTime = Date.now();
        const url = `${apiBaseUrl}/qrcode/download?format=${format}`;
        try {
            const response = await fetch(url, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(options), signal });
            const endTime = Date.now();
            updateRequestDetails('POST', url, response.status, endTime - startTime, options);

            if (!response.ok) {
                let errorText = 'Falha ao gerar preview';
                try { const errJson = await response.json(); errorText = errJson.message || errorText; } catch(_) {}
                container.innerHTML = `<div class=\"qr-placeholder\" style=\"color:#e74c3c;\">❌ ${errorText}</div>`;
                return;
            }

            if (format === 'svg') {
                const text = await response.text();
                container.innerHTML = text;
            } else {
                const blob = await response.blob();
                if (__lastObjectUrl) URL.revokeObjectURL(__lastObjectUrl);
                const imageUrl = URL.createObjectURL(blob);
                __lastObjectUrl = imageUrl;
                container.innerHTML = `<img src=\"${imageUrl}\" alt=\"QR Code\">`;
            }
        } catch (err) {
            if (err.name === 'AbortError') return;
            container.innerHTML = `<div class=\"qr-placeholder\" style=\"color:#e74c3c;\">❌ ${err.message}</div>`;
        }
    }

    // Bind listeners after page load without overriding existing onload
    window.addEventListener('load', () => {
        // Hook inputs for live preview
        const ids = ['qr-text','qr-width','qr-height','qr-margin','qr-shape','error-correction','qr-format','dot-type','dot-color','background-color','corner-square-type','corner-dot-type','corner-square-color','corner-dot-color','gradient-color1','gradient-color2','gradient-type','gradient-rotation','image-size'];
        ids.forEach(id => {
            const el = document.getElementById(id);
            if (!el) return;
            const eventName = (el.tagName === 'SELECT' || id === 'qr-format') ? 'change' : 'input';
            el.addEventListener(eventName, () => {
                if (id === 'gradient-rotation') document.getElementById('gradient-rotation-value').textContent = el.value + '°';
                if (id === 'image-size') document.getElementById('image-size-value').textContent = el.value + '%';
                schedulePreviewUpdate();
            });
        });
        const useGradient = document.getElementById('use-gradient');
        if (useGradient) useGradient.addEventListener('change', () => { toggleGradient(); schedulePreviewUpdate(); });
        toggleGradient();
        schedulePreviewUpdate();
    });
</script>
<script>
    // Override handleImageUpload to integrate with live preview and image preview
    function handleImageUpload(input) {
        const file = input.files[0];
        if (!file) return;
        if (!file.type.startsWith('image/')) { showMessage('Por favor, selecione apenas arquivos de imagem.', 'error'); return; }
        if (file.size > 5 * 1024 * 1024) { showMessage('A imagem deve ter no máximo 5MB.', 'error'); return; }
        const reader = new FileReader();
        reader.onload = function(e) {
            currentImageData = e.target.result;
            const preview = document.getElementById('image-preview');
            if (preview) { preview.src = currentImageData; preview.style.display = 'block'; }
            schedulePreviewUpdate();
        };
        reader.onerror = function() { showMessage('Erro ao carregar a imagem.', 'error'); };
        reader.readAsDataURL(file);
    }
</script>
</body>
</html>
