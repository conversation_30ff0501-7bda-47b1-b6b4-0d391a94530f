<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comparação QR Code - Frontend vs API</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
        }
        h1 {
            text-align: center;
            color: #333;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 30px;
        }
        .panel {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
        }
        .panel h2 {
            color: #667eea;
            margin-bottom: 20px;
        }
        .qr-container {
            border: 2px dashed #e0e0e0;
            border-radius: 10px;
            padding: 20px;
            min-height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
        }
        .controls {
            margin-top: 20px;
            padding: 15px;
            background: white;
            border-radius: 10px;
        }
        .control-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            border: 2px solid #e0e0e0;
            border-radius: 5px;
        }
        button {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #5a6fd8;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Comparação: Frontend JS vs API Backend</h1>
        
        <div class="controls">
            <div class="control-group">
                <label>Texto do QR Code:</label>
                <textarea id="qr-text" rows="2">https://github.com/qr-code-styling</textarea>
            </div>
            <div class="control-group">
                <label>Tipo de Ponto:</label>
                <select id="dot-type">
                    <option value="square">Quadrado</option>
                    <option value="rounded">Arredondado</option>
                    <option value="dots">Círculo</option>
                    <option value="classy">Elegante</option>
                    <option value="classy-rounded">Elegante Arredondado</option>
                    <option value="extra-rounded">Extra Arredondado</option>
                </select>
            </div>
            <div class="control-group">
                <label>Cor dos Pontos:</label>
                <input type="color" id="dot-color" value="#000000">
            </div>
            <div class="control-group">
                <label>Tipo de Canto Quadrado:</label>
                <select id="corner-square-type">
                    <option value="square">Quadrado</option>
                    <option value="extra-rounded">Extra Arredondado</option>
                    <option value="dot">Ponto</option>
                </select>
            </div>
            <div class="control-group">
                <label>Tipo de Canto Ponto:</label>
                <select id="corner-dot-type">
                    <option value="square">Quadrado</option>
                    <option value="dot">Ponto</option>
                </select>
            </div>
            <div class="control-group">
                <label>Usar Gradiente:</label>
                <input type="checkbox" id="use-gradient">
            </div>
            <div>
                <button onclick="generateBoth()">🔄 Gerar Ambos</button>
                <button onclick="applyPreset('modern')">🎨 Preset Moderno</button>
                <button onclick="applyPreset('pix')">💳 Preset PIX</button>
            </div>
        </div>
        
        <div class="comparison">
            <div class="panel">
                <h2>📦 Frontend (qr-code-styling.js)</h2>
                <div id="frontend-container" class="qr-container">
                    <span>Aguardando geração...</span>
                </div>
                <div id="frontend-status" class="status"></div>
            </div>
            
            <div class="panel">
                <h2>🚀 API Backend</h2>
                <div id="api-container" class="qr-container">
                    <span>Aguardando geração...</span>
                </div>
                <div id="api-status" class="status"></div>
            </div>
        </div>
    </div>

    <!-- Carregar biblioteca do frontend -->
    <script src="dist/qr-code-styling.js"></script>
    
    <script>
        let qrCodeInstance = null;
        const API_URL = 'http://localhost:3000/api/v1';
        
        const presets = {
            modern: {
                dotsOptions: { type: 'rounded', color: '#667eea' },
                cornersSquareOptions: { type: 'extra-rounded', color: '#764ba2' },
                cornersDotOptions: { type: 'dot', color: '#667eea' }
            },
            pix: {
                dotsOptions: { type: 'square', color: '#000000' },
                cornersSquareOptions: { type: 'square', color: '#000000' },
                cornersDotOptions: { type: 'square', color: '#000000' }
            }
        };
        
        function applyPreset(name) {
            const preset = presets[name];
            if (preset) {
                document.getElementById('dot-type').value = preset.dotsOptions.type;
                document.getElementById('dot-color').value = preset.dotsOptions.color;
                if (preset.cornersSquareOptions) {
                    document.getElementById('corner-square-type').value = preset.cornersSquareOptions.type;
                }
                if (preset.cornersDotOptions) {
                    document.getElementById('corner-dot-type').value = preset.cornersDotOptions.type;
                }
                generateBoth();
            }
        }
        
        function getOptions() {
            const options = {
                data: document.getElementById('qr-text').value.trim(),
                width: 300,
                height: 300,
                dotsOptions: {
                    type: document.getElementById('dot-type').value,
                    color: document.getElementById('dot-color').value
                },
                backgroundOptions: {
                    color: '#ffffff'
                },
                cornersSquareOptions: {
                    type: document.getElementById('corner-square-type')?.value || 'square',
                    color: document.getElementById('dot-color').value
                },
                cornersDotOptions: {
                    type: document.getElementById('corner-dot-type')?.value || 'square',
                    color: document.getElementById('dot-color').value
                }
            };
            
            if (document.getElementById('use-gradient').checked) {
                options.dotsOptions.gradient = {
                    type: 'linear',
                    rotation: 0,
                    colorStops: [
                        { offset: 0, color: '#667eea' },
                        { offset: 1, color: '#764ba2' }
                    ]
                };
                delete options.dotsOptions.color;
            }
            
            return options;
        }
        
        async function generateFrontend() {
            const container = document.getElementById('frontend-container');
            const status = document.getElementById('frontend-status');
            
            try {
                const startTime = Date.now();
                const options = getOptions();
                
                container.innerHTML = '';
                qrCodeInstance = new QRCodeStyling(options);
                qrCodeInstance.append(container);
                
                const elapsedTime = Date.now() - startTime;
                status.className = 'status success';
                status.textContent = `✅ Gerado em ${elapsedTime}ms`;
            } catch (error) {
                status.className = 'status error';
                status.textContent = `❌ Erro: ${error.message}`;
            }
        }
        
        async function generateAPI() {
            const container = document.getElementById('api-container');
            const status = document.getElementById('api-status');
            
            try {
                const startTime = Date.now();
                const options = getOptions();
                
                container.innerHTML = '<span>Carregando...</span>';
                
                const response = await fetch(`${API_URL}/qrcode/download?format=png`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(options)
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                
                const blob = await response.blob();
                const imageUrl = URL.createObjectURL(blob);
                
                container.innerHTML = `<img src="${imageUrl}" alt="QR Code API">`;
                
                const elapsedTime = Date.now() - startTime;
                status.className = 'status success';
                status.textContent = `✅ Gerado em ${elapsedTime}ms (${blob.size} bytes)`;
            } catch (error) {
                status.className = 'status error';
                status.textContent = `❌ Erro: ${error.message}`;
            }
        }
        
        async function generateBoth() {
            await Promise.all([
                generateFrontend(),
                generateAPI()
            ]);
        }
        
        // Gerar ao carregar a página
        window.onload = function() {
            if (typeof QRCodeStyling === 'undefined') {
                alert('Erro: Biblioteca QRCodeStyling não foi carregada.');
                return;
            }
            generateBoth();
        };
    </script>
</body>
</html>
