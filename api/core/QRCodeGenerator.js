import qrcode from 'qrcode-generator';
import { createCanvas, loadImage } from 'canvas';
import { J<PERSON><PERSON> } from 'jsdom';
import { QRCodeResult } from '../domain/entities/QRCodeResult.js';
import { createError, extractErrorMessage } from '../utils/helpers.js';
import { SVGGenerator } from './SVGGenerator.js';

// Máscaras para os finder patterns (corners)
const squareMask = [
  [1, 1, 1, 1, 1, 1, 1],
  [1, 0, 0, 0, 0, 0, 1],
  [1, 0, 0, 0, 0, 0, 1],
  [1, 0, 0, 0, 0, 0, 1],
  [1, 0, 0, 0, 0, 0, 1],
  [1, 0, 0, 0, 0, 0, 1],
  [1, 1, 1, 1, 1, 1, 1]
];

const dotMask = [
  [0, 0, 0, 0, 0, 0, 0],
  [0, 0, 0, 0, 0, 0, 0],
  [0, 0, 1, 1, 1, 0, 0],
  [0, 0, 1, 1, 1, 0, 0],
  [0, 0, 1, 1, 1, 0, 0],
  [0, 0, 0, 0, 0, 0, 0],
  [0, 0, 0, 0, 0, 0, 0]
];

/**
 * Core QR Code Generator
 * Handles the actual QR code generation using different renderers
 */
export class QRCodeGenerator {
  constructor() {
    this.svgGenerator = new SVGGenerator();
  }

  /**
   * Generate QR code with specified options
   * @param {QRCodeOptions} options QR code options entity
   * @returns {Promise<QRCodeResult>} Generated QR code result
   */
  async generate(options) {
    try {
      // Generate base QR code data
      const qrCodeData = this.generateQRCodeData(options);

      // Choose renderer based on type
      let result;
      if (options.type === 'svg') {
        result = await this.generateSVG(qrCodeData, options);
      } else {
        result = await this.generateCanvas(qrCodeData, options);
      }

      return result;
    } catch (error) {
      console.error('QR Generation Error:', error);
      throw createError(
        'QR code generation failed',
        'CORE_GENERATION_ERROR',
        { originalError: extractErrorMessage(error) }
      );
    }
  }

  /**
   * Generate base QR code data matrix
   * @param {QRCodeOptions} options QR code options
   * @returns {Object} QR code data and metadata
   */
  generateQRCodeData(options) {
    try {
      const qr = qrcode(options.qrOptions.typeNumber, options.qrOptions.errorCorrectionLevel);
      qr.addData(options.data, options.qrOptions.mode);
      qr.make();

      const moduleCount = qr.getModuleCount();
      const modules = [];

      // Extract module data
      for (let row = 0; row < moduleCount; row++) {
        modules[row] = [];
        for (let col = 0; col < moduleCount; col++) {
          modules[row][col] = qr.isDark(row, col);
        }
      }

      return {
        qr,
        modules,
        moduleCount,
        metadata: {
          typeNumber: qr.typeNumber || options.qrOptions.typeNumber,
          errorCorrectionLevel: options.qrOptions.errorCorrectionLevel,
          dataLength: options.data.length,
        },
      };
    } catch (error) {
      throw createError(
        'Failed to generate QR code data',
        'QR_DATA_GENERATION_ERROR',
        { originalError: extractErrorMessage(error) }
      );
    }
  }

  /**
   * Generate SVG QR code
   * @param {Object} qrCodeData QR code data
   * @param {QRCodeOptions} options QR code options
   * @returns {Promise<QRCodeResult>} SVG result
   */
  async generateSVG(qrCodeData, options) {
    try {
      // Create JSDOM window for SVG manipulation
      const dom = new JSDOM('<!DOCTYPE html><html><body></body></html>');
      const window = dom.window;
      global.window = window;
      global.document = window.document;

      // Generate SVG using SVG generator
      const svgString = await this.svgGenerator.generate(qrCodeData, options, window);

      // Clean up global references
      delete global.window;
      delete global.document;

      return QRCodeResult.fromString(svgString, 'svg', {
        moduleCount: qrCodeData.moduleCount,
        ...qrCodeData.metadata,
      });
    } catch (error) {
      throw createError(
        'SVG generation failed',
        'SVG_GENERATION_ERROR',
        { originalError: extractErrorMessage(error) }
      );
    }
  }

  /**
   * Generate Canvas QR code
   * @param {Object} qrCodeData QR code data
   * @param {QRCodeOptions} options QR code options
   * @returns {Promise<QRCodeResult>} Canvas result
   */
  async generateCanvas(qrCodeData, options) {
    try {
      const { modules, moduleCount } = qrCodeData;

      // Create canvas
      const canvas = createCanvas(options.width, options.height);
      const ctx = canvas.getContext('2d');

      // Calculate sizes
      const availableWidth = options.width - 2 * (options.margin || 0);
      const availableHeight = options.height - 2 * (options.margin || 0);
      const moduleSize = Math.floor(Math.min(availableWidth, availableHeight) / moduleCount);
      const qrSize = moduleSize * moduleCount;
      const offsetX = Math.floor((options.width - qrSize) / 2);
      const offsetY = Math.floor((options.height - qrSize) / 2);

      // Clear canvas and add background
      const backgroundColor = options.backgroundOptions?.color || '#ffffff';
      ctx.fillStyle = backgroundColor;
      ctx.fillRect(0, 0, options.width, options.height);

      // Apply circular shape mask if specified
      if (options.shape === 'circle') {
        ctx.save();
        ctx.globalCompositeOperation = 'destination-in';
        ctx.beginPath();
        const centerX = options.width / 2;
        const centerY = options.height / 2;
        const radius = Math.min(options.width, options.height) / 2;
        ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
        ctx.fill();
        ctx.globalCompositeOperation = 'source-over';
      }

      // Set drawing style for QR modules
      const dotColor = options.dotsOptions?.color || '#000000';
      const dotType = options.dotsOptions?.type || 'square';
      const gradient = options.dotsOptions?.gradient;
      
      // Debug log
      console.log('QRCodeGenerator - dotType:', dotType, 'dotsOptions:', options.dotsOptions);

      // Create gradient if specified
      let fillStyle = dotColor;
      if (gradient) {
        fillStyle = this.createGradient(ctx, gradient, offsetX, offsetY, qrSize);
      }

      // Get corner options
      const cornerSquareColor = options.cornersSquareOptions?.color || dotColor;
      const cornerSquareType = options.cornersSquareOptions?.type || 'square';
      const cornerDotColor = options.cornersDotOptions?.color || cornerSquareColor;
      const cornerDotType = options.cornersDotOptions?.type || 'square';

      // Draw QR modules
      for (let row = 0; row < moduleCount; row++) {
        for (let col = 0; col < moduleCount; col++) {
          if (!modules[row][col]) continue;

          // Check if this position is part of a finder pattern using masks
          const isInSquareMask = this.isInFinderPatternMask(row, col, moduleCount, squareMask);
          const isInDotMask = this.isInFinderPatternMask(row, col, moduleCount, dotMask);
          
          // Skip regular dots where finder patterns are
          if (isInSquareMask || isInDotMask) continue;
          
          const x = offsetX + col * moduleSize;
          const y = offsetY + row * moduleSize;
          
          ctx.fillStyle = fillStyle;
          
          // Create neighbor checking function
          const getNeighbor = (xOffset, yOffset) => {
            const newRow = row + yOffset;
            const newCol = col + xOffset;
            
            if (newRow < 0 || newRow >= moduleCount || newCol < 0 || newCol >= moduleCount) {
              return false;
            }
            
            // Don't consider finder pattern positions as neighbors
            if (this.isInFinderPatternMask(newRow, newCol, moduleCount, squareMask)) {
              return false;
            }
            if (this.isInFinderPatternMask(newRow, newCol, moduleCount, dotMask)) {
              return false;
            }
            
            return modules[newRow][newCol];
          };
          
          this.drawModule(ctx, x, y, moduleSize, dotType, getNeighbor);
        }
      }
      
      // Now draw the corner patterns separately
      this.drawCornerPatterns(ctx, modules, moduleCount, options, offsetX, offsetY, moduleSize);

      // Add image overlay if specified
      if (options.image) {
        try {
          const image = await loadImage(options.image);
          const imageSize = qrSize * (options.imageOptions?.imageSize || 0.4);
          const imageX = offsetX + (qrSize - imageSize) / 2;
          const imageY = offsetY + (qrSize - imageSize) / 2;

          // Create mask if needed
          if (options.imageOptions?.hideBackgroundDots) {
            ctx.save();
            ctx.globalCompositeOperation = 'destination-out';
            ctx.beginPath();
            ctx.arc(imageX + imageSize / 2, imageY + imageSize / 2, imageSize / 2 + 10, 0, 2 * Math.PI);
            ctx.fill();
            ctx.restore();
          }

          ctx.drawImage(image, imageX, imageY, imageSize, imageSize);
        } catch (imageError) {
          console.warn('Failed to load image:', imageError.message);
        }
      }

      // Restore context if shape was circular
      if (options.shape === 'circle') {
        ctx.restore();

        // Re-apply circular mask to final result
        ctx.globalCompositeOperation = 'destination-in';
        ctx.beginPath();
        const centerX = options.width / 2;
        const centerY = options.height / 2;
        const radius = Math.min(options.width, options.height) / 2;
        ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
        ctx.fill();
        ctx.globalCompositeOperation = 'source-over';
      }

      // Get buffer based on format
      const format = options.format || 'png';
      let buffer;

      switch (format) {
        case 'png':
          buffer = canvas.toBuffer('image/png');
          break;
        case 'jpeg':
          buffer = canvas.toBuffer('image/jpeg', { quality: 0.9 });
          break;
        case 'webp':
          buffer = canvas.toBuffer('image/webp', { quality: 0.9 });
          break;
        default:
          buffer = canvas.toBuffer('image/png');
      }

      return QRCodeResult.fromBuffer(buffer, format, {
        moduleCount,
        dimensions: { width: options.width, height: options.height },
        ...qrCodeData.metadata,
      });
    } catch (error) {
      console.error('Canvas generation error:', error);
      throw createError(
        'Canvas generation failed',
        'CANVAS_GENERATION_ERROR',
        { originalError: extractErrorMessage(error) }
      );
    }
  }

  /**
   * Load and process image for QR code overlay
   * @param {string} imageSrc Image source (URL or data URL)
   * @returns {Promise<Image>} Loaded image
   */
  async loadImage(imageSrc) {
    try {
      if (!imageSrc) return null;

      const image = await loadImage(imageSrc);
      return image;
    } catch (error) {
      throw createError(
        'Failed to load image',
        'IMAGE_LOAD_ERROR',
        { imageSrc, originalError: extractErrorMessage(error) }
      );
    }
  }

  /**
   * Calculate module size based on QR dimensions
   * @param {QRCodeOptions} options QR code options
   * @param {number} moduleCount Number of modules
   * @returns {Object} Size calculations
   */
  calculateSizes(options, moduleCount) {
    const availableWidth = options.width - 2 * options.margin;
    const availableHeight = options.height - 2 * options.margin;

    const moduleSize = Math.floor(Math.min(availableWidth, availableHeight) / moduleCount);
    const qrSize = moduleSize * moduleCount;

    const offsetX = Math.floor((options.width - qrSize) / 2);
    const offsetY = Math.floor((options.height - qrSize) / 2);

    return {
      moduleSize,
      qrSize,
      offsetX,
      offsetY,
      availableWidth,
      availableHeight,
    };
  }

  /**
   * Check if position is in a finder pattern using masks
   * @param {number} row Row position
   * @param {number} col Column position
   * @param {number} moduleCount Total module count
   * @param {Array} mask The mask to check against
   * @returns {boolean} Is in pattern
   */
  isInFinderPatternMask(row, col, moduleCount, mask) {
    // Check top-left corner
    if (row < 7 && col < 7) {
      return mask[row]?.[col] === 1;
    }
    
    // Check top-right corner
    if (row < 7 && col >= moduleCount - 7) {
      return mask[row]?.[col - moduleCount + 7] === 1;
    }
    
    // Check bottom-left corner
    if (row >= moduleCount - 7 && col < 7) {
      return mask[row - moduleCount + 7]?.[col] === 1;
    }
    
    return false;
  }

  /**
   * Draw corner patterns (finder patterns)
   */
  drawCornerPatterns(ctx, modules, moduleCount, options, offsetX, offsetY, moduleSize) {
    const cornerSquareColor = options.cornersSquareOptions?.color || options.dotsOptions?.color || '#000000';
    const cornerSquareType = options.cornersSquareOptions?.type || 'square';
    const cornerDotColor = options.cornersDotOptions?.color || cornerSquareColor;
    const cornerDotType = options.cornersDotOptions?.type || 'square';
    
    // Define the three corner positions
    const corners = [
      { row: 0, col: 0 },           // Top-left
      { row: 0, col: moduleCount - 7 },  // Top-right  
      { row: moduleCount - 7, col: 0 }   // Bottom-left
    ];
    
    corners.forEach(corner => {
      const cornerX = offsetX + corner.col * moduleSize;
      const cornerY = offsetY + corner.row * moduleSize;
      const cornerSize = 7 * moduleSize;
      
      // Draw the outer square based on type
      ctx.fillStyle = cornerSquareColor;
      
      if (cornerSquareType === 'extra-rounded') {
        // Draw as a single large rounded shape
        this.drawRoundedSquareWithHole(ctx, cornerX, cornerY, cornerSize, moduleSize);
      } else if (cornerSquareType === 'dot') {
        // Draw as circles at the corners
        const dotPositions = [
          [0, 0], [0, 6], [6, 0], [6, 6],
          [0, 1], [1, 0], [0, 5], [1, 6],
          [5, 0], [6, 1], [5, 6], [6, 5]
        ];
        dotPositions.forEach(([r, c]) => {
          const x = cornerX + c * moduleSize;
          const y = cornerY + r * moduleSize;
          ctx.beginPath();
          ctx.arc(x + moduleSize/2, y + moduleSize/2, moduleSize/2, 0, 2 * Math.PI);
          ctx.fill();
        });
      } else {
        // Default square pattern - draw individual modules
        for (let r = 0; r < 7; r++) {
          for (let c = 0; c < 7; c++) {
            if (squareMask[r][c] === 1) {
              const x = cornerX + c * moduleSize;
              const y = cornerY + r * moduleSize;
              ctx.fillRect(x, y, moduleSize, moduleSize);
            }
          }
        }
      }
      
      // Draw the center dot (3x3 area)
      const centerX = cornerX + 2 * moduleSize;
      const centerY = cornerY + 2 * moduleSize;
      const centerSize = 3 * moduleSize;
      
      ctx.fillStyle = cornerDotColor;
      
      if (cornerDotType === 'dot') {
        // Draw as a single circle
        ctx.beginPath();
        ctx.arc(centerX + centerSize/2, centerY + centerSize/2, centerSize/2, 0, 2 * Math.PI);
        ctx.fill();
      } else {
        // Draw as square
        ctx.fillRect(centerX, centerY, centerSize, centerSize);
      }
    });
  }

  /**
   * Check if position is a corner square center
   * @param {number} row Row position
   * @param {number} col Column position
   * @param {number} moduleCount Total module count
   * @returns {boolean} Is corner square center
   */
  isCornerSquareCenter(row, col, moduleCount) {
    // Centers of finder patterns
    if ((row === 3 && col === 3) ||
        (row === 3 && col === moduleCount - 4) ||
        (row === moduleCount - 4 && col === 3)) {
      return true;
    }
    return false;
  }

  /**
   * Get neighbor modules for shape calculation
   * @param {Array} modules Module matrix
   * @param {number} row Row position
   * @param {number} col Column position
   * @returns {Function} Neighbor checking function
   */
  getNeighborFunction(modules, row, col) {
    return (dRow, dCol) => {
      const newRow = row + dRow;
      const newCol = col + dCol;

      if (newRow < 0 || newRow >= modules.length ||
          newCol < 0 || newCol >= modules[0].length) {
        return false;
      }

      return modules[newRow][newCol];
    };
  }

  /**
   * Draw rounded square with hole (for corner patterns)
   * @param {CanvasRenderingContext2D} ctx Canvas context
   * @param {number} x X position
   * @param {number} y Y position
   * @param {number} size Size of the square
   * @param {number} moduleSize Size of each module
   */
  drawRoundedSquareWithHole(ctx, x, y, size, moduleSize) {
    const outerRadius = size / 3.5;
    const innerSize = 3 * moduleSize;
    const innerOffset = 2 * moduleSize;
    const innerRadius = innerSize / 3.5;
    
    ctx.save();
    
    // Draw outer rounded square
    ctx.beginPath();
    ctx.moveTo(x + outerRadius, y);
    ctx.lineTo(x + size - outerRadius, y);
    ctx.quadraticCurveTo(x + size, y, x + size, y + outerRadius);
    ctx.lineTo(x + size, y + size - outerRadius);
    ctx.quadraticCurveTo(x + size, y + size, x + size - outerRadius, y + size);
    ctx.lineTo(x + outerRadius, y + size);
    ctx.quadraticCurveTo(x, y + size, x, y + size - outerRadius);
    ctx.lineTo(x, y + outerRadius);
    ctx.quadraticCurveTo(x, y, x + outerRadius, y);
    ctx.closePath();
    
    // Cut out inner rounded square (create a hole)
    ctx.moveTo(x + innerOffset + innerRadius, y + innerOffset);
    ctx.lineTo(x + innerOffset + innerSize - innerRadius, y + innerOffset);
    ctx.quadraticCurveTo(x + innerOffset + innerSize, y + innerOffset, x + innerOffset + innerSize, y + innerOffset + innerRadius);
    ctx.lineTo(x + innerOffset + innerSize, y + innerOffset + innerSize - innerRadius);
    ctx.quadraticCurveTo(x + innerOffset + innerSize, y + innerOffset + innerSize, x + innerOffset + innerSize - innerRadius, y + innerOffset + innerSize);
    ctx.lineTo(x + innerOffset + innerRadius, y + innerOffset + innerSize);
    ctx.quadraticCurveTo(x + innerOffset, y + innerOffset + innerSize, x + innerOffset, y + innerOffset + innerSize - innerRadius);
    ctx.lineTo(x + innerOffset, y + innerOffset + innerRadius);
    ctx.quadraticCurveTo(x + innerOffset, y + innerOffset, x + innerOffset + innerRadius, y + innerOffset);
    
    ctx.fillRule = 'evenodd';
    ctx.fill();
    ctx.restore();
  }
  
  /**
   * Draw rounded rectangle on canvas
   * @param {CanvasRenderingContext2D} ctx Canvas context
   * @param {number} x X position
   * @param {number} y Y position
   * @param {number} width Width
   * @param {number} height Height
   * @param {number} radius Corner radius
   */
  drawRoundedRect(ctx, x, y, width, height, radius) {
    ctx.beginPath();
    ctx.moveTo(x + radius, y);
    ctx.lineTo(x + width - radius, y);
    ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
    ctx.lineTo(x + width, y + height - radius);
    ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
    ctx.lineTo(x + radius, y + height);
    ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
    ctx.lineTo(x, y + radius);
    ctx.quadraticCurveTo(x, y, x + radius, y);
    ctx.closePath();
    ctx.fill();
  }

  /**
   * Draw a single module based on type
   * @param {CanvasRenderingContext2D} ctx Canvas context
   * @param {number} x X position
   * @param {number} y Y position
   * @param {number} size Module size
   * @param {string} type Module type
   * @param {Function} getNeighbor Optional function to check neighbors
   */
  drawModule(ctx, x, y, size, type, getNeighbor) {
    switch (type) {
      case 'dots':
      case 'dot':
        // Draw circular dots
        ctx.beginPath();
        const radius = size / 2;
        ctx.arc(x + size / 2, y + size / 2, radius, 0, 2 * Math.PI);
        ctx.fill();
        break;
      
      case 'rounded':
        // For rounded, check neighbors if function provided
        if (getNeighbor) {
          const leftNeighbor = getNeighbor(-1, 0) ? 1 : 0;
          const rightNeighbor = getNeighbor(1, 0) ? 1 : 0;
          const topNeighbor = getNeighbor(0, -1) ? 1 : 0;
          const bottomNeighbor = getNeighbor(0, 1) ? 1 : 0;
          const neighborsCount = leftNeighbor + rightNeighbor + topNeighbor + bottomNeighbor;
          
          if (neighborsCount === 0) {
            // No neighbors - draw circle
            ctx.beginPath();
            ctx.arc(x + size / 2, y + size / 2, size / 2, 0, 2 * Math.PI);
            ctx.fill();
          } else if (neighborsCount > 2 || (leftNeighbor && rightNeighbor) || (topNeighbor && bottomNeighbor)) {
            // Many neighbors or opposite neighbors - draw square
            ctx.fillRect(x, y, size, size);
          } else {
            // Some neighbors - draw rounded rectangle
            this.drawRoundedRect(ctx, x, y, size, size, size / 4);
          }
        } else {
          this.drawRoundedRect(ctx, x, y, size, size, size / 4);
        }
        break;
      
      case 'extra-rounded':
        // Draw extra rounded rectangles (more rounded corners)
        if (getNeighbor) {
          const leftNeighbor = getNeighbor(-1, 0) ? 1 : 0;
          const rightNeighbor = getNeighbor(1, 0) ? 1 : 0;
          const topNeighbor = getNeighbor(0, -1) ? 1 : 0;
          const bottomNeighbor = getNeighbor(0, 1) ? 1 : 0;
          const neighborsCount = leftNeighbor + rightNeighbor + topNeighbor + bottomNeighbor;
          
          if (neighborsCount === 0) {
            ctx.beginPath();
            ctx.arc(x + size / 2, y + size / 2, size / 2, 0, 2 * Math.PI);
            ctx.fill();
          } else if (neighborsCount > 2 || (leftNeighbor && rightNeighbor) || (topNeighbor && bottomNeighbor)) {
            ctx.fillRect(x, y, size, size);
          } else {
            this.drawRoundedRect(ctx, x, y, size, size, size / 2.5);
          }
        } else {
          this.drawRoundedRect(ctx, x, y, size, size, size / 2.5);
        }
        break;
      
      case 'classy':
        // Draw classy style (rounded square with small inset)
        const inset = size * 0.1;
        this.drawRoundedRect(ctx, x + inset, y + inset, size - 2 * inset, size - 2 * inset, size / 6);
        break;
      
      case 'classy-rounded':
        // Draw classy-rounded style
        const classyInset = size * 0.08;
        this.drawRoundedRect(ctx, x + classyInset, y + classyInset, size - 2 * classyInset, size - 2 * classyInset, size / 3);
        break;
      
      case 'square':
      default:
        // Draw square modules (default)
        ctx.fillRect(x, y, size, size);
        break;
    }
  }

  /**
   * Create gradient fill style
   * @param {CanvasRenderingContext2D} ctx Canvas context
   * @param {Object} gradient Gradient options
   * @param {number} x X position
   * @param {number} y Y position
   * @param {number} size Size
   * @returns {CanvasGradient} Gradient object
   */
  createGradient(ctx, gradient, x, y, size) {
    let grad;
    
    if (gradient.type === 'radial') {
      const centerX = x + size / 2;
      const centerY = y + size / 2;
      grad = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, size / 2);
    } else {
      // Linear gradient
      const rotation = gradient.rotation || 0;
      const x0 = x + size / 2 - Math.cos(rotation) * size / 2;
      const y0 = y + size / 2 - Math.sin(rotation) * size / 2;
      const x1 = x + size / 2 + Math.cos(rotation) * size / 2;
      const y1 = y + size / 2 + Math.sin(rotation) * size / 2;
      grad = ctx.createLinearGradient(x0, y0, x1, y1);
    }

    // Add color stops
    if (gradient.colorStops && Array.isArray(gradient.colorStops)) {
      gradient.colorStops.forEach(stop => {
        grad.addColorStop(stop.offset || 0, stop.color || '#000000');
      });
    } else {
      grad.addColorStop(0, '#000000');
      grad.addColorStop(1, '#ffffff');
    }

    return grad;
  }

  /**
   * Check if position is in the center of a finder pattern
   * @param {number} row Row position
   * @param {number} col Column position
   * @param {number} moduleCount Total module count
   * @returns {boolean} Is finder pattern center
   */
  isFinderPatternCenter(row, col, moduleCount) {
    // Centers of the three finder patterns (3x3 inner squares)
    const positions = [
      { r: 3, c: 3 },                          // Top-left
      { r: 3, c: moduleCount - 4 },            // Top-right
      { r: moduleCount - 4, c: 3 }             // Bottom-left
    ];

    for (let pos of positions) {
      if (row >= pos.r && row <= pos.r + 2 && col >= pos.c && col <= pos.c + 2) {
        return true;
      }
    }

    return false;
  }
}