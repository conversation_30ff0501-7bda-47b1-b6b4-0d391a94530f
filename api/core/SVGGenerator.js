import { createError, extractErrorMessage } from '../utils/helpers.js';

/**
 * SVG Generator
 * Handles SVG-specific QR code generation
 */
export class SVGGenerator {
  /**
   * Generate SVG QR code
   * @param {Object} qrCodeData QR code data
   * @param {QRCodeOptions} options QR code options
   * @param {Window} window DOM window object
   * @returns {Promise<string>} SVG string
   */
  async generate(qrCodeData, options, window) {
    try {
      const { modules, moduleCount } = qrCodeData;
      const sizes = this.calculateSizes(options, moduleCount);

      // Create SVG element
      const svg = this.createSVGElement(window, options);

      // Add background
      this.addBackground(svg, window, options, sizes);

      // Add QR modules (dots)
      this.addDots(svg, window, modules, options, sizes);

      // Add corner squares
      this.addCornerSquares(svg, window, modules, options, sizes);

      // Add corner dots
      this.addCornerDots(svg, window, modules, options, sizes);

      // Add image overlay if specified
      if (options.image) {
        await this.addImage(svg, window, options, sizes);
      }

      return svg.outerHTML;
    } catch (error) {
      throw createError(
        'SVG generation failed',
        'SVG_GENERATION_ERROR',
        { originalError: extractErrorMessage(error) }
      );
    }
  }

  /**
   * Calculate sizes for SVG generation
   * @param {QRCodeOptions} options QR code options
   * @param {number} moduleCount Module count
   * @returns {Object} Size calculations
   */
  calculateSizes(options, moduleCount) {
    const availableWidth = options.width - 2 * options.margin;
    const availableHeight = options.height - 2 * options.margin;

    const moduleSize = Math.min(availableWidth, availableHeight) / moduleCount;
    const qrSize = moduleSize * moduleCount;

    const offsetX = (options.width - qrSize) / 2;
    const offsetY = (options.height - qrSize) / 2;

    return {
      moduleSize,
      qrSize,
      offsetX,
      offsetY,
    };
  }

  /**
   * Create base SVG element
   * @param {Window} window DOM window
   * @param {QRCodeOptions} options QR code options
   * @returns {SVGElement} SVG element
   */
  createSVGElement(window, options) {
    const svg = window.document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    svg.setAttribute('width', options.width.toString());
    svg.setAttribute('height', options.height.toString());
    svg.setAttribute('viewBox', `0 0 ${options.width} ${options.height}`);
    svg.setAttribute('xmlns', 'http://www.w3.org/2000/svg');

    if (options.shape === 'circle') {
      svg.setAttribute('style', 'border-radius: 50%; overflow: hidden;');
    }

    return svg;
  }

  /**
   * Add background to SVG
   * @param {SVGElement} svg SVG element
   * @param {Window} window DOM window
   * @param {QRCodeOptions} options QR code options
   * @param {Object} sizes Size calculations
   */
  addBackground(svg, window, options, sizes) {
    // Apply circular clipping if shape is circle
    if (options.shape === 'circle') {
      // Create clip path
      const defs = window.document.createElementNS('http://www.w3.org/2000/svg', 'defs');
      const clipPath = window.document.createElementNS('http://www.w3.org/2000/svg', 'clipPath');
      clipPath.setAttribute('id', 'circle-clip');
      
      const clipCircle = window.document.createElementNS('http://www.w3.org/2000/svg', 'circle');
      clipCircle.setAttribute('cx', (options.width / 2).toString());
      clipCircle.setAttribute('cy', (options.height / 2).toString());
      clipCircle.setAttribute('r', (Math.min(options.width, options.height) / 2).toString());
      
      clipPath.appendChild(clipCircle);
      defs.appendChild(clipPath);
      svg.appendChild(defs);
      
      // Create a group with the clip path applied
      const clipGroup = window.document.createElementNS('http://www.w3.org/2000/svg', 'g');
      clipGroup.setAttribute('clip-path', 'url(#circle-clip)');
      svg.appendChild(clipGroup);
    }

    const bg = window.document.createElementNS('http://www.w3.org/2000/svg', 'rect');
    bg.setAttribute('width', options.width.toString());
    bg.setAttribute('height', options.height.toString());

    if (options.backgroundOptions.gradient) {
      const gradientId = this.addGradient(svg, window, options.backgroundOptions.gradient, 'bg');
      bg.setAttribute('fill', `url(#${gradientId})`);
    } else {
      bg.setAttribute('fill', options.backgroundOptions.color);
    }

    if (options.backgroundOptions.round > 0) {
      bg.setAttribute('rx', options.backgroundOptions.round.toString());
      bg.setAttribute('ry', options.backgroundOptions.round.toString());
    }

    if (options.shape === 'circle') {
      // Add background to clipped group
      const clipGroup = svg.querySelector('g[clip-path]');
      clipGroup.appendChild(bg);
    } else {
      svg.appendChild(bg);
    }
  }

  /**
   * Add QR code dots to SVG
   * @param {SVGElement} svg SVG element
   * @param {Window} window DOM window
   * @param {Array} modules Module matrix
   * @param {QRCodeOptions} options QR code options
   * @param {Object} sizes Size calculations
   */
  addDots(svg, window, modules, options, sizes) {
    const dotsGroup = window.document.createElementNS('http://www.w3.org/2000/svg', 'g');
    dotsGroup.setAttribute('class', 'qr-dots');

    for (let row = 0; row < modules.length; row++) {
      for (let col = 0; col < modules[row].length; col++) {
        if (modules[row][col] && !this.isInFinderPattern(row, col, modules.length)) {
          const dot = this.createDot(window, row, col, options, sizes);
          if (dot) dotsGroup.appendChild(dot);
        }
      }
    }

    if (options.shape === 'circle') {
      // Add dots to clipped group
      const clipGroup = svg.querySelector('g[clip-path]');
      clipGroup.appendChild(dotsGroup);
    } else {
      svg.appendChild(dotsGroup);
    }
  }

  /**
   * Create individual dot element
   * @param {Window} window DOM window
   * @param {number} row Row position
   * @param {number} col Column position
   * @param {QRCodeOptions} options QR code options
   * @param {Object} sizes Size calculations
   * @returns {SVGElement} Dot element
   */
  createDot(window, row, col, options, sizes) {
    const x = sizes.offsetX + col * sizes.moduleSize;
    const y = sizes.offsetY + row * sizes.moduleSize;
    const size = sizes.moduleSize;

    let element;

    switch (options.dotsOptions.type) {
      case 'dots':
        element = window.document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        element.setAttribute('cx', (x + size / 2).toString());
        element.setAttribute('cy', (y + size / 2).toString());
        element.setAttribute('r', (size / 2.5).toString());
        break;

      case 'rounded':
      case 'extra-rounded':
        element = window.document.createElementNS('http://www.w3.org/2000/svg', 'rect');
        element.setAttribute('x', x.toString());
        element.setAttribute('y', y.toString());
        element.setAttribute('width', size.toString());
        element.setAttribute('height', size.toString());
        const radius = options.dotsOptions.type === 'extra-rounded' ? size / 2 : size / 4;
        element.setAttribute('rx', radius.toString());
        element.setAttribute('ry', radius.toString());
        break;

      case 'classy':
      case 'classy-rounded':
        element = this.createClassyDot(window, x, y, size, options.dotsOptions.type === 'classy-rounded');
        break;

      case 'square':
      default:
        element = window.document.createElementNS('http://www.w3.org/2000/svg', 'rect');
        element.setAttribute('x', x.toString());
        element.setAttribute('y', y.toString());
        element.setAttribute('width', size.toString());
        element.setAttribute('height', size.toString());
        break;
    }

    // Apply color or gradient
    if (options.dotsOptions.gradient) {
      const gradientId = this.addGradient(svg, window, options.dotsOptions.gradient, 'dots');
      element.setAttribute('fill', `url(#${gradientId})`);
    } else {
      element.setAttribute('fill', options.dotsOptions.color);
    }

    return element;
  }

  /**
   * Create classy dot with custom path
   * @param {Window} window DOM window
   * @param {number} x X position
   * @param {number} y Y position
   * @param {number} size Module size
   * @param {boolean} rounded Whether to round corners
   * @returns {SVGElement} Path element
   */
  createClassyDot(window, x, y, size, rounded = false) {
    const element = window.document.createElementNS('http://www.w3.org/2000/svg', 'path');

    let path;
    if (rounded) {
      const r = size * 0.1;
      path = `M${x + r},${y} h${size - 2 * r} a${r},${r} 0 0 1 ${r},${r} v${size - 2 * r} a${r},${r} 0 0 1 -${r},${r} h-${size - 2 * r} a${r},${r} 0 0 1 -${r},-${r} v-${size - 2 * r} a${r},${r} 0 0 1 ${r},-${r} z`;
    } else {
      path = `M${x},${y} h${size} v${size} h-${size} z`;
    }

    element.setAttribute('d', path);
    return element;
  }

  /**
   * Add corner squares to SVG
   * @param {SVGElement} svg SVG element
   * @param {Window} window DOM window
   * @param {Array} modules Module matrix
   * @param {QRCodeOptions} options QR code options
   * @param {Object} sizes Size calculations
   */
  addCornerSquares(svg, window, modules, options, sizes) {
    if (!options.cornersSquareOptions.type) return;

    const corners = [
      { row: 0, col: 0 }, // Top-left
      { row: 0, col: modules.length - 7 }, // Top-right
      { row: modules.length - 7, col: 0 }, // Bottom-left
    ];

    corners.forEach(corner => {
      const square = this.createCornerSquare(window, corner.row, corner.col, options, sizes);
      if (square) svg.appendChild(square);
    });
  }

  /**
   * Create corner square element
   * @param {Window} window DOM window
   * @param {number} row Row position
   * @param {number} col Column position
   * @param {QRCodeOptions} options QR code options
   * @param {Object} sizes Size calculations
   * @returns {SVGElement} Corner square element
   */
  createCornerSquare(window, row, col, options, sizes) {
    const x = sizes.offsetX + col * sizes.moduleSize;
    const y = sizes.offsetY + row * sizes.moduleSize;
    const size = sizes.moduleSize * 7;

    let element;

    switch (options.cornersSquareOptions.type) {
      case 'dot':
        element = window.document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        element.setAttribute('cx', (x + size / 2).toString());
        element.setAttribute('cy', (y + size / 2).toString());
        element.setAttribute('r', (size / 2).toString());
        break;

      case 'extra-rounded':
        element = window.document.createElementNS('http://www.w3.org/2000/svg', 'rect');
        element.setAttribute('x', x.toString());
        element.setAttribute('y', y.toString());
        element.setAttribute('width', size.toString());
        element.setAttribute('height', size.toString());
        element.setAttribute('rx', (size / 2).toString());
        element.setAttribute('ry', (size / 2).toString());
        break;

      case 'square':
      default:
        element = window.document.createElementNS('http://www.w3.org/2000/svg', 'rect');
        element.setAttribute('x', x.toString());
        element.setAttribute('y', y.toString());
        element.setAttribute('width', size.toString());
        element.setAttribute('height', size.toString());
        break;
    }

    // Apply color or gradient
    if (options.cornersSquareOptions.gradient) {
      const gradientId = this.addGradient(svg, window, options.cornersSquareOptions.gradient, 'corner-square');
      element.setAttribute('fill', `url(#${gradientId})`);
    } else if (options.cornersSquareOptions.color) {
      element.setAttribute('fill', options.cornersSquareOptions.color);
    }

    return element;
  }

  /**
   * Add corner dots to SVG
   * @param {SVGElement} svg SVG element
   * @param {Window} window DOM window
   * @param {Array} modules Module matrix
   * @param {QRCodeOptions} options QR code options
   * @param {Object} sizes Size calculations
   */
  addCornerDots(svg, window, modules, options, sizes) {
    if (!options.cornersDotOptions.type) return;

    const corners = [
      { row: 3, col: 3 }, // Top-left
      { row: 3, col: modules.length - 4 }, // Top-right
      { row: modules.length - 4, col: 3 }, // Bottom-left
    ];

    corners.forEach(corner => {
      const dot = this.createCornerDot(window, corner.row, corner.col, options, sizes);
      if (dot) svg.appendChild(dot);
    });
  }

  /**
   * Create corner dot element
   * @param {Window} window DOM window
   * @param {number} row Row position
   * @param {number} col Column position
   * @param {QRCodeOptions} options QR code options
   * @param {Object} sizes Size calculations
   * @returns {SVGElement} Corner dot element
   */
  createCornerDot(window, row, col, options, sizes) {
    const x = sizes.offsetX + col * sizes.moduleSize;
    const y = sizes.offsetY + row * sizes.moduleSize;
    const size = sizes.moduleSize * 3;

    let element;

    switch (options.cornersDotOptions.type) {
      case 'dot':
        element = window.document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        element.setAttribute('cx', (x + size / 2).toString());
        element.setAttribute('cy', (y + size / 2).toString());
        element.setAttribute('r', (size / 2).toString());
        break;

      case 'square':
      default:
        element = window.document.createElementNS('http://www.w3.org/2000/svg', 'rect');
        element.setAttribute('x', x.toString());
        element.setAttribute('y', y.toString());
        element.setAttribute('width', size.toString());
        element.setAttribute('height', size.toString());
        break;
    }

    // Apply color or gradient
    if (options.cornersDotOptions.gradient) {
      const gradientId = this.addGradient(svg, window, options.cornersDotOptions.gradient, 'corner-dot');
      element.setAttribute('fill', `url(#${gradientId})`);
    } else if (options.cornersDotOptions.color) {
      element.setAttribute('fill', options.cornersDotOptions.color);
    }

    return element;
  }

  /**
   * Add image overlay to SVG
   * @param {SVGElement} svg SVG element
   * @param {Window} window DOM window
   * @param {QRCodeOptions} options QR code options
   * @param {Object} sizes Size calculations
   */
  async addImage(svg, window, options, sizes) {
    if (!options.image) return;

    const imageSize = sizes.qrSize * options.imageOptions.imageSize;
    const imageX = sizes.offsetX + (sizes.qrSize - imageSize) / 2;
    const imageY = sizes.offsetY + (sizes.qrSize - imageSize) / 2;

    const image = window.document.createElementNS('http://www.w3.org/2000/svg', 'image');
    image.setAttribute('x', imageX.toString());
    image.setAttribute('y', imageY.toString());
    image.setAttribute('width', imageSize.toString());
    image.setAttribute('height', imageSize.toString());
    image.setAttribute('href', options.image);

    if (options.imageOptions.crossOrigin) {
      image.setAttribute('crossorigin', options.imageOptions.crossOrigin);
    }

    svg.appendChild(image);
  }

  /**
   * Add gradient definition to SVG
   * @param {SVGElement} svg SVG element
   * @param {Window} window DOM window
   * @param {Object} gradient Gradient options
   * @param {string} prefix ID prefix
   * @returns {string} Gradient ID
   */
  addGradient(svg, window, gradient, prefix) {
    const gradientId = `${prefix}-gradient-${Math.random().toString(36).substr(2, 9)}`;

    let defs = svg.querySelector('defs');
    if (!defs) {
      defs = window.document.createElementNS('http://www.w3.org/2000/svg', 'defs');
      svg.appendChild(defs);
    }

    let gradientElement;
    if (gradient.type === 'radial') {
      gradientElement = window.document.createElementNS('http://www.w3.org/2000/svg', 'radialGradient');
    } else {
      gradientElement = window.document.createElementNS('http://www.w3.org/2000/svg', 'linearGradient');
      if (gradient.rotation) {
        gradientElement.setAttribute('gradientTransform', `rotate(${gradient.rotation * 180 / Math.PI})`);
      }
    }

    gradientElement.setAttribute('id', gradientId);

    gradient.colorStops.forEach(stop => {
      const stopElement = window.document.createElementNS('http://www.w3.org/2000/svg', 'stop');
      stopElement.setAttribute('offset', `${stop.offset * 100}%`);
      stopElement.setAttribute('stop-color', stop.color);
      gradientElement.appendChild(stopElement);
    });

    defs.appendChild(gradientElement);
    return gradientId;
  }

  /**
   * Check if position is in finder pattern
   * @param {number} row Row position
   * @param {number} col Column position
   * @param {number} moduleCount Module count
   * @returns {boolean} Is in finder pattern
   */
  isInFinderPattern(row, col, moduleCount) {
    return (
      (row < 9 && col < 9) ||
      (row < 9 && col >= moduleCount - 8) ||
      (row >= moduleCount - 8 && col < 9)
    );
  }
}