
> qr-code-styling-api@1.0.0 start
> node index.js

🚀 QR Code Styling API server running on http://localhost:3000
📖 API Documentation: http://localhost:3000/api/v1/docs
🏥 Health Check: http://localhost:3000/health
🌍 Environment: development

📋 Quick Test Commands:
curl "http://localhost:3000/api/v1/qrcode/quick?data=Hello%20World"
curl -X POST "http://localhost:3000/api/v1/qrcode/generate" \
  -H "Content-Type: application/json" \
  -d '{"data":"Hello World","width":300,"height":300}'
QRCodeGenerator - dotType: dots dotsOptions: {
  type: 'dots',
  color: '#000000',
  gradient: undefined,
  roundSize: true
}
127.0.0.1 - - [21/Sep/2025:23:32:34 +0000] "POST /api/v1/qrcode/download?format=png HTTP/1.1" 200 6776 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36"
SIGTERM received, shutting down gracefully
Process terminated
