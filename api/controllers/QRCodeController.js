import { QRCodeService } from '../services/QRCodeService.js';
import { validateQRCodeOptions } from '../utils/validation.js';
import { createError, createSuccessResponse, extractErrorMessage } from '../utils/helpers.js';
import config from '../config/env.js';

/**
 * QR Code Controller
 * Handles HTTP requests for QR code generation
 */
export class QRCodeController {
  constructor() {
    this.qrCodeService = new QRCodeService();
  }

  /**
   * Generate QR Code
   * POST /api/v1/qrcode/generate
   */
  async generate(req, res) {
    try {
      let options = req.body;

      // Apply preset if specified
      if (req.query.preset) {
        const presetOptions = this.getPresetOptions(req.query.preset);
        if (presetOptions) {
          options = { ...presetOptions, ...options, data: options.data || presetOptions.data };
        }
      }

      // Validate input options
      const validation = validateQRCodeOptions(options);
      if (validation.errors.length > 0) {
        return res.status(400).json(
          createError(
            'Invalid QR code options',
            'VALIDATION_ERROR',
            { errors: validation.errors, warnings: validation.warnings }
          )
        );
      }

      // Generate QR code
      const result = await this.qrCodeService.generateQRCode(options);

      // Return success response
      res.status(200).json(
        createSuccessResponse(result, 'QR code generated successfully')
      );
    } catch (error) {
      console.error('Error generating QR code:', error);
      res.status(500).json(
        createError(
          'Failed to generate QR code',
          'GENERATION_ERROR',
          { originalError: extractErrorMessage(error) }
        )
      );
    }
  }

  /**
   * Generate QR Code and return as file
   * POST /api/v1/qrcode/download
   */
  async download(req, res) {
    try {
      let options = req.body;
      const format = req.query.format || 'png';

      // Apply preset if specified
      if (req.query.preset) {
        const presetOptions = this.getPresetOptions(req.query.preset);
        if (presetOptions) {
          options = { ...presetOptions, ...options, data: options.data || presetOptions.data };
        }
      }

      // Validate format
      const validFormats = ['png', 'jpeg', 'webp', 'svg'];
      if (!validFormats.includes(format)) {
        return res.status(400).json(
          createError(
            `Invalid format. Must be one of: ${validFormats.join(', ')}`,
            'INVALID_FORMAT'
          )
        );
      }

      // Validate input options
      const validation = validateQRCodeOptions(options);
      if (validation.errors.length > 0) {
        return res.status(400).json(
          createError(
            'Invalid QR code options',
            'VALIDATION_ERROR',
            { errors: validation.errors }
          )
        );
      }

      // Generate QR code with specified format
      const result = await this.qrCodeService.generateQRCode({ ...options, format });

      // Set response headers for file download
      const filename = req.query.filename || `qrcode.${result.getFileExtension()}`;
      res.set({
        'Content-Type': result.getContentType(),
        'Content-Disposition': `attachment; filename=\"${filename}\"`,
        'Content-Length': result.size,
      });

      // Send file data
      if (format === 'svg') {
        res.send(result.getString());
      } else {
        res.send(result.getBuffer());
      }
    } catch (error) {
      console.error('Error downloading QR code:', error);
      res.status(500).json(
        createError(
          'Failed to generate QR code for download',
          'DOWNLOAD_ERROR',
          { originalError: extractErrorMessage(error) }
        )
      );
    }
  }

  /**
   * Generate QR Code from URL parameters
   * GET /api/v1/qrcode/quick
   */
  async quick(req, res) {
    try {
      const {
        data,
        size = config.qrCode.defaultSize,
        format = 'png',
        color = '#000',
        background = '#fff',
        margin = 0,
      } = req.query;

      // Validate required data parameter
      if (!data) {
        return res.status(400).json(
          createError('Data parameter is required', 'MISSING_DATA')
        );
      }

      // Build options from query parameters
      const options = {
        data,
        width: parseInt(size),
        height: parseInt(size),
        margin: parseInt(margin),
        dotsOptions: { color },
        backgroundOptions: { color: background },
      };

      // Validate options
      const validation = validateQRCodeOptions(options);
      if (validation.errors.length > 0) {
        return res.status(400).json(
          createError(
            'Invalid parameters',
            'VALIDATION_ERROR',
            { errors: validation.errors }
          )
        );
      }

      // Generate QR code
      const result = await this.qrCodeService.generateQRCode({
        ...options,
        format,
        type: format === 'svg' ? 'svg' : 'canvas'
      });

      // Set response headers
      res.set({
        'Content-Type': result.getContentType(),
        'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
      });

      // Send result
      if (format === 'svg') {
        res.send(result.getString());
      } else {
        res.send(result.getBuffer());
      }
    } catch (error) {
      console.error('Error in quick QR generation:', error);
      res.status(500).json(
        createError(
          'Failed to generate QR code',
          'QUICK_GENERATION_ERROR',
          { originalError: extractErrorMessage(error) }
        )
      );
    }
  }

  /**
   * Generate batch QR codes
   * POST /api/v1/qrcode/batch
   */
  async batch(req, res) {
    try {
      const { items, commonOptions = {} } = req.body;

      // Validate input
      if (!Array.isArray(items) || items.length === 0) {
        return res.status(400).json(
          createError('Items must be a non-empty array', 'INVALID_BATCH_INPUT')
        );
      }

      if (items.length > 100) {
        return res.status(400).json(
          createError('Batch size cannot exceed 100 items', 'BATCH_SIZE_EXCEEDED')
        );
      }

      // Process batch
      const results = await this.qrCodeService.generateBatch(items, commonOptions);

      res.status(200).json(
        createSuccessResponse(results, 'Batch QR codes generated successfully')
      );
    } catch (error) {
      console.error('Error in batch QR generation:', error);
      res.status(500).json(
        createError(
          'Failed to generate batch QR codes',
          'BATCH_GENERATION_ERROR',
          { originalError: extractErrorMessage(error) }
        )
      );
    }
  }

  /**
   * Get QR Code generation options and limits
   * GET /api/v1/qrcode/options
   */
  async getOptions(req, res) {
    try {
      const options = {
        limits: {
          maxSize: config.qrCode.maxSize,
          minSize: config.qrCode.minSize,
          defaultSize: config.qrCode.defaultSize,
          maxDataLength: config.qrCode.maxDataLength,
        },
        supportedFormats: ['png', 'jpeg', 'webp', 'svg'],
        supportedTypes: ['canvas', 'svg'],
        supportedShapes: ['square', 'circle'],
        dotTypes: ['square', 'dots', 'rounded', 'classy', 'classy-rounded', 'extra-rounded'],
        cornerTypes: ['square', 'dot', 'extra-rounded'],
        errorCorrectionLevels: ['L', 'M', 'Q', 'H'],
        gradientTypes: ['linear', 'radial'],
      };

      res.status(200).json(
        createSuccessResponse(options, 'QR code options retrieved successfully')
      );
    } catch (error) {
      console.error('Error retrieving options:', error);
      res.status(500).json(
        createError(
          'Failed to retrieve options',
          'OPTIONS_ERROR',
          { originalError: extractErrorMessage(error) }
        )
      );
    }
  }

  /**
   * Health check endpoint
   * GET /api/v1/qrcode/health
   */
  async health(req, res) {
    try {
      // Test basic QR generation
      const testResult = await this.qrCodeService.generateQRCode({
        data: 'health-check',
        width: 100,
        height: 100,
      });

      res.status(200).json(
        createSuccessResponse(
          {
            status: 'healthy',
            timestamp: new Date().toISOString(),
            generationTest: testResult ? 'passed' : 'failed',
          },
          'Service is healthy'
        )
      );
    } catch (error) {
      console.error('Health check failed:', error);
      res.status(503).json(
        createError(
          'Service unhealthy',
          'HEALTH_CHECK_FAILED',
          { originalError: extractErrorMessage(error) }
        )
      );
    }
  }

  /**
   * Get preset options for QR code generation
   * @param {string} presetName Name of the preset
   * @returns {Object|null} Preset options or null if not found
   */
  getPresetOptions(presetName) {
    const presets = {
      pix: {
        dotsOptions: {
          type: 'square',
          color: '#000000'
        },
        backgroundOptions: {
          color: '#ffffff'
        },
        cornersSquareOptions: {
          type: 'square',
          color: '#000000'
        },
        cornersDotOptions: {
          type: 'square',
          color: '#000000'
        },
        margin: 16,
        qrOptions: {
          errorCorrectionLevel: 'L'
        }
      },
      classic: {
        dotsOptions: {
          type: 'square',
          color: '#000000'
        },
        backgroundOptions: {
          color: '#ffffff'
        },
        cornersSquareOptions: {
          type: 'square',
          color: '#000000'
        },
        cornersDotOptions: {
          type: 'square',
          color: '#000000'
        }
      },
      modern: {
        dotsOptions: {
          type: 'rounded',
          color: '#667eea'
        },
        backgroundOptions: {
          color: '#f8f9fa'
        },
        cornersSquareOptions: {
          type: 'extra-rounded',
          color: '#764ba2'
        },
        cornersDotOptions: {
          type: 'dot',
          color: '#667eea'
        }
      },
      colorful: {
        dotsOptions: {
          type: 'dots',
          gradient: {
            type: 'linear',
            rotation: 0,
            colorStops: [
              { offset: 0, color: '#ff6b6b' },
              { offset: 1, color: '#4ecdc4' }
            ]
          }
        },
        backgroundOptions: {
          color: '#fff3cd'
        },
        cornersSquareOptions: {
          type: 'extra-rounded',
          color: '#ffd93d'
        },
        cornersDotOptions: {
          type: 'dot',
          color: '#ff6b6b'
        }
      },
      minimal: {
        dotsOptions: {
          type: 'classy',
          color: '#2c3e50'
        },
        backgroundOptions: {
          color: '#ffffff'
        },
        cornersSquareOptions: {
          type: 'square',
          color: '#34495e'
        },
        cornersDotOptions: {
          type: 'square',
          color: '#2c3e50'
        }
      },
      corporate: {
        dotsOptions: {
          type: 'classy-rounded',
          color: '#1e3a8a'
        },
        backgroundOptions: {
          color: '#f1f5f9'
        },
        cornersSquareOptions: {
          type: 'extra-rounded',
          color: '#1e40af'
        },
        cornersDotOptions: {
          type: 'dot',
          color: '#1e3a8a'
        }
      }
    };

    return presets[presetName] || null;
  }
}