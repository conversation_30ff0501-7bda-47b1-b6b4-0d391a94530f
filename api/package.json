{"name": "qr-code-styling-api", "version": "1.0.0", "description": "QR Code Styling API - Modular backend for QR code generation with styling", "main": "index.js", "type": "module", "engines": {"node": ">=18.18.0"}, "scripts": {"start": "node index.js", "dev": "nodemon index.js", "build": "node scripts/build.js", "test": "jest", "test:unit": "jest tests/unit", "test:integration": "jest tests/integration", "test:e2e": "jest tests/e2e", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "docs:serve": "node docs/serve.js"}, "dependencies": {"canvas": "^3.2.0", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "joi": "^17.11.0", "jsdom": "^25.0.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "qrcode-generator": "^1.4.4"}, "devDependencies": {"@babel/core": "^7.23.6", "@babel/preset-env": "^7.23.6", "babel-jest": "^29.7.0", "eslint": "^8.55.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "prettier": "^3.1.0", "supertest": "^6.3.3"}, "keywords": ["qr-code", "api", "styling", "backend", "nodejs", "rest-api", "modular", "clean-architecture"], "author": "QR Code Styling API Team", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/qr-code-styling/api.git"}}