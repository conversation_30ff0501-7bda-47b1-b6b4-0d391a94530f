<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comparação Final - Frontend JS vs Backend API</title>
    <style>
        body {
            font-family: 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px;
            margin: 0;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
        }
        
        h1 {
            text-align: center;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 30px;
        }
        
        .controls {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 30px;
        }
        
        .control-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .control-group {
            display: flex;
            flex-direction: column;
        }
        
        label {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }
        
        input, select, textarea {
            padding: 8px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .qr-panel {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
        }
        
        .qr-panel h2 {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .qr-container {
            border: 2px dashed #e0e0e0;
            border-radius: 10px;
            padding: 20px;
            min-height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
        }
        
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
            font-size: 12px;
        }
        
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        
        button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            margin: 5px;
        }
        
        button:hover {
            opacity: 0.9;
        }
        
        .preset-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        
        .test-results {
            margin-top: 30px;
            background: #e8f4f8;
            padding: 20px;
            border-radius: 10px;
        }
        
        .test-results h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .test-item {
            display: flex;
            justify-content: space-between;
            padding: 10px;
            background: white;
            margin-bottom: 10px;
            border-radius: 5px;
        }
        
        .test-pass { color: #27ae60; }
        .test-fail { color: #e74c3c; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Comparação Final: Frontend JS vs Backend API</h1>
        
        <div class="controls">
            <div class="control-row">
                <div class="control-group">
                    <label>Texto do QR Code:</label>
                    <textarea id="qr-text" rows="2">https://github.com/qr-code-styling</textarea>
                </div>
            </div>
            
            <div class="control-row">
                <div class="control-group">
                    <label>Tipo de Ponto:</label>
                    <select id="dot-type">
                        <option value="square">Quadrado</option>
                        <option value="rounded">Arredondado</option>
                        <option value="dots">Círculo</option>
                        <option value="classy">Elegante</option>
                        <option value="classy-rounded">Elegante Arredondado</option>
                        <option value="extra-rounded">Extra Arredondado</option>
                    </select>
                </div>
                
                <div class="control-group">
                    <label>Cor dos Pontos:</label>
                    <input type="color" id="dot-color" value="#000000">
                </div>
                
                <div class="control-group">
                    <label>Tipo de Canto (Quadrado):</label>
                    <select id="corner-square-type">
                        <option value="square">Quadrado</option>
                        <option value="extra-rounded">Extra Arredondado</option>
                        <option value="dot">Ponto</option>
                    </select>
                </div>
                
                <div class="control-group">
                    <label>Tipo de Canto (Centro):</label>
                    <select id="corner-dot-type">
                        <option value="square">Quadrado</option>
                        <option value="dot">Ponto</option>
                    </select>
                </div>
                
                <div class="control-group">
                    <label>Cor de Fundo:</label>
                    <input type="color" id="background-color" value="#ffffff">
                </div>
            </div>
            
            <div class="preset-buttons">
                <button onclick="applyPreset('pix')">💳 PIX</button>
                <button onclick="applyPreset('classic')">⬛ Clássico</button>
                <button onclick="applyPreset('modern')">🎨 Moderno</button>
                <button onclick="applyPreset('colorful')">🌈 Colorido</button>
                <button onclick="applyPreset('minimal')">⚪ Minimalista</button>
                <button onclick="applyPreset('corporate')">💼 Corporativo</button>
            </div>
            
            <div style="text-align: center;">
                <button onclick="generateBoth()">🔄 Gerar Ambos</button>
                <button onclick="runAllTests()">🧪 Executar Todos os Testes</button>
            </div>
        </div>
        
        <div class="comparison-grid">
            <div class="qr-panel">
                <h2>📦 Frontend (qr-code-styling.js)</h2>
                <div id="frontend-container" class="qr-container">
                    <span>Aguardando geração...</span>
                </div>
                <div id="frontend-status" class="status"></div>
            </div>
            
            <div class="qr-panel">
                <h2>🚀 Backend API</h2>
                <div id="api-container" class="qr-container">
                    <span>Aguardando geração...</span>
                </div>
                <div id="api-status" class="status"></div>
            </div>
        </div>
        
        <div id="test-results" class="test-results" style="display: none;">
            <h3>📊 Resultados dos Testes</h3>
            <div id="test-list"></div>
        </div>
    </div>

    <!-- Carregar biblioteca do frontend -->
    <script src="dist/qr-code-styling.js"></script>
    
    <script>
        let qrCodeInstance = null;
        const API_URL = 'http://localhost:3000/api/v1';
        
        const presets = {
            pix: {
                dotsOptions: { type: 'square', color: '#000000' },
                cornersSquareOptions: { type: 'square', color: '#000000' },
                cornersDotOptions: { type: 'square', color: '#000000' },
                backgroundOptions: { color: '#ffffff' }
            },
            classic: {
                dotsOptions: { type: 'square', color: '#000000' },
                cornersSquareOptions: { type: 'square', color: '#000000' },
                cornersDotOptions: { type: 'square', color: '#000000' },
                backgroundOptions: { color: '#ffffff' }
            },
            modern: {
                dotsOptions: { type: 'rounded', color: '#667eea' },
                cornersSquareOptions: { type: 'extra-rounded', color: '#764ba2' },
                cornersDotOptions: { type: 'dot', color: '#667eea' },
                backgroundOptions: { color: '#f8f9fa' }
            },
            colorful: {
                dotsOptions: { type: 'dots', color: '#ff6b6b' },
                cornersSquareOptions: { type: 'extra-rounded', color: '#ffd93d' },
                cornersDotOptions: { type: 'dot', color: '#ff6b6b' },
                backgroundOptions: { color: '#fff3cd' }
            },
            minimal: {
                dotsOptions: { type: 'classy', color: '#2c3e50' },
                cornersSquareOptions: { type: 'square', color: '#34495e' },
                cornersDotOptions: { type: 'square', color: '#2c3e50' },
                backgroundOptions: { color: '#ffffff' }
            },
            corporate: {
                dotsOptions: { type: 'classy-rounded', color: '#1e3a8a' },
                cornersSquareOptions: { type: 'extra-rounded', color: '#1e40af' },
                cornersDotOptions: { type: 'dot', color: '#1e3a8a' },
                backgroundOptions: { color: '#f1f5f9' }
            }
        };
        
        function applyPreset(name) {
            const preset = presets[name];
            if (!preset) return;
            
            document.getElementById('dot-type').value = preset.dotsOptions.type;
            document.getElementById('dot-color').value = preset.dotsOptions.color;
            document.getElementById('corner-square-type').value = preset.cornersSquareOptions.type;
            document.getElementById('corner-dot-type').value = preset.cornersDotOptions.type;
            document.getElementById('background-color').value = preset.backgroundOptions.color;
            
            generateBoth();
        }
        
        function getOptions() {
            return {
                data: document.getElementById('qr-text').value.trim(),
                width: 300,
                height: 300,
                type: 'canvas',
                margin: 0,
                dotsOptions: {
                    type: document.getElementById('dot-type').value,
                    color: document.getElementById('dot-color').value
                },
                backgroundOptions: {
                    color: document.getElementById('background-color').value
                },
                cornersSquareOptions: {
                    type: document.getElementById('corner-square-type').value,
                    color: document.getElementById('dot-color').value
                },
                cornersDotOptions: {
                    type: document.getElementById('corner-dot-type').value,
                    color: document.getElementById('dot-color').value
                },
                qrOptions: {
                    typeNumber: 0,
                    errorCorrectionLevel: 'M'
                }
            };
        }
        
        async function generateFrontend() {
            const container = document.getElementById('frontend-container');
            const status = document.getElementById('frontend-status');
            
            try {
                const startTime = Date.now();
                const options = getOptions();
                
                if (!options.data) {
                    container.innerHTML = '<span>Digite um texto</span>';
                    return;
                }
                
                container.innerHTML = '';
                qrCodeInstance = new QRCodeStyling(options);
                qrCodeInstance.append(container);
                
                const elapsedTime = Date.now() - startTime;
                status.className = 'status success';
                status.textContent = `✅ Gerado em ${elapsedTime}ms`;
            } catch (error) {
                status.className = 'status error';
                status.textContent = `❌ Erro: ${error.message}`;
            }
        }
        
        async function generateAPI() {
            const container = document.getElementById('api-container');
            const status = document.getElementById('api-status');
            
            try {
                const startTime = Date.now();
                const options = getOptions();
                
                if (!options.data) {
                    container.innerHTML = '<span>Digite um texto</span>';
                    return;
                }
                
                container.innerHTML = '<span>Carregando...</span>';
                
                const response = await fetch(`${API_URL}/qrcode/download?format=png`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(options)
                });
                
                if (!response.ok) throw new Error(`HTTP ${response.status}`);
                
                const blob = await response.blob();
                const imageUrl = URL.createObjectURL(blob);
                
                container.innerHTML = `<img src="${imageUrl}" alt="QR Code API">`;
                
                const elapsedTime = Date.now() - startTime;
                status.className = 'status success';
                status.textContent = `✅ Gerado em ${elapsedTime}ms (${blob.size} bytes)`;
            } catch (error) {
                status.className = 'status error';
                status.textContent = `❌ Erro: ${error.message}`;
            }
        }
        
        async function generateBoth() {
            await Promise.all([
                generateFrontend(),
                generateAPI()
            ]);
        }
        
        async function runAllTests() {
            const resultsDiv = document.getElementById('test-results');
            const testList = document.getElementById('test-list');
            
            resultsDiv.style.display = 'block';
            testList.innerHTML = '<div>Executando testes...</div>';
            
            const tests = [];
            
            // Test each preset
            for (const [name, preset] of Object.entries(presets)) {
                const testName = `Preset ${name}`;
                
                // Apply preset
                document.getElementById('dot-type').value = preset.dotsOptions.type;
                document.getElementById('dot-color').value = preset.dotsOptions.color;
                document.getElementById('corner-square-type').value = preset.cornersSquareOptions.type;
                document.getElementById('corner-dot-type').value = preset.cornersDotOptions.type;
                document.getElementById('background-color').value = preset.backgroundOptions.color;
                
                // Generate both
                await generateBoth();
                
                // Wait a bit for rendering
                await new Promise(resolve => setTimeout(resolve, 500));
                
                // Check if both generated successfully
                const frontendStatus = document.getElementById('frontend-status').textContent;
                const apiStatus = document.getElementById('api-status').textContent;
                
                const pass = frontendStatus.includes('✅') && apiStatus.includes('✅');
                
                tests.push({
                    name: testName,
                    pass: pass,
                    message: pass ? 'Ambos gerados com sucesso' : 'Falha na geração'
                });
            }
            
            // Display results
            testList.innerHTML = tests.map(test => `
                <div class="test-item">
                    <span>${test.name}</span>
                    <span class="${test.pass ? 'test-pass' : 'test-fail'}">
                        ${test.pass ? '✅' : '❌'} ${test.message}
                    </span>
                </div>
            `).join('');
            
            // Summary
            const passed = tests.filter(t => t.pass).length;
            const total = tests.length;
            
            testList.innerHTML += `
                <div style="margin-top: 20px; padding: 15px; background: ${passed === total ? '#d4edda' : '#f8d7da'}; border-radius: 5px;">
                    <strong>Resumo:</strong> ${passed} de ${total} testes passaram
                    ${passed === total ? '🎉 Todos os testes passaram!' : '⚠️ Alguns testes falharam'}
                </div>
            `;
        }
        
        // Initialize on load
        window.onload = function() {
            if (typeof QRCodeStyling === 'undefined') {
                alert('Erro: Biblioteca QRCodeStyling não foi carregada.');
                return;
            }
            generateBoth();
        };
    </script>
</body>
</html>
