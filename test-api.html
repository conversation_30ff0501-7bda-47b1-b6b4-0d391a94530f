<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste de Personalização - QR Code API</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 40px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 10px;
        }

        .subtitle {
            text-align: center;
            color: #666;
            margin-bottom: 40px;
            font-size: 14px;
        }

        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }

        .test-card {
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            background: #f9f9f9;
        }

        .test-card h3 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 18px;
        }

        .qr-display {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            min-height: 240px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .qr-display img, .qr-display svg {
            max-width: 200px;
            max-height: 200px;
        }

        .config-info {
            background: #f0f0f0;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            text-align: left;
            color: #555;
            font-family: monospace;
        }

        .status {
            padding: 5px 10px;
            border-radius: 5px;
            margin-top: 10px;
            font-size: 12px;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
        }

        .status.loading {
            background: #cce5ff;
            color: #004085;
        }

        .test-button {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
            font-size: 16px;
        }

        .test-button:hover {
            background: #5a67d8;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Teste de Personalização do QR Code</h1>
        <p class="subtitle">Testando todas as opções de personalização da API</p>

        <div style="text-align: center; margin-bottom: 30px;">
            <button class="test-button" onclick="runAllTests()">🚀 Executar Todos os Testes</button>
            <button class="test-button" onclick="clearTests()">🗑️ Limpar Resultados</button>
        </div>

        <div class="test-grid" id="test-grid">
            <!-- Teste 1: Tipos de Pontos -->
            <div class="test-card">
                <h3>Teste 1: Tipos de Pontos</h3>
                <div class="qr-display" id="test1-display">
                    <span style="color: #999;">Aguardando...</span>
                </div>
                <div class="config-info">
                    dotsOptions.type: "rounded"<br>
                    dotsOptions.color: "#4267B2"
                </div>
                <div class="status" id="test1-status"></div>
            </div>

            <!-- Teste 2: Cantos Personalizados -->
            <div class="test-card">
                <h3>Teste 2: Cantos Personalizados</h3>
                <div class="qr-display" id="test2-display">
                    <span style="color: #999;">Aguardando...</span>
                </div>
                <div class="config-info">
                    cornersSquareOptions.type: "extra-rounded"<br>
                    cornersSquareOptions.color: "#FF0000"<br>
                    cornersDotOptions.type: "dot"<br>
                    cornersDotOptions.color: "#00FF00"
                </div>
                <div class="status" id="test2-status"></div>
            </div>

            <!-- Teste 3: Gradiente Linear -->
            <div class="test-card">
                <h3>Teste 3: Gradiente Linear</h3>
                <div class="qr-display" id="test3-display">
                    <span style="color: #999;">Aguardando...</span>
                </div>
                <div class="config-info">
                    dotsOptions.gradient.type: "linear"<br>
                    colorStops: ["#FF6B6B", "#4ECDC4"]<br>
                    rotation: 45°
                </div>
                <div class="status" id="test3-status"></div>
            </div>

            <!-- Teste 4: Gradiente Radial -->
            <div class="test-card">
                <h3>Teste 4: Gradiente Radial</h3>
                <div class="qr-display" id="test4-display">
                    <span style="color: #999;">Aguardando...</span>
                </div>
                <div class="config-info">
                    dotsOptions.gradient.type: "radial"<br>
                    colorStops: ["#667eea", "#764ba2"]
                </div>
                <div class="status" id="test4-status"></div>
            </div>

            <!-- Teste 5: Tipo Classy -->
            <div class="test-card">
                <h3>Teste 5: Estilo Classy</h3>
                <div class="qr-display" id="test5-display">
                    <span style="color: #999;">Aguardando...</span>
                </div>
                <div class="config-info">
                    dotsOptions.type: "classy"<br>
                    cornersSquareOptions.type: "classy"
                </div>
                <div class="status" id="test5-status"></div>
            </div>

            <!-- Teste 6: Formato SVG -->
            <div class="test-card">
                <h3>Teste 6: Formato SVG</h3>
                <div class="qr-display" id="test6-display">
                    <span style="color: #999;">Aguardando...</span>
                </div>
                <div class="config-info">
                    format: "svg"<br>
                    type: "svg"<br>
                    dotsOptions.type: "dots"
                </div>
                <div class="status" id="test6-status"></div>
            </div>
        </div>
    </div>

    <script>
        const apiUrl = 'http://localhost:3000/api/v1';

        const tests = [
            {
                id: 'test1',
                name: 'Tipos de Pontos',
                options: {
                    data: 'Teste de Pontos Arredondados',
                    width: 200,
                    height: 200,
                    dotsOptions: {
                        type: 'rounded',
                        color: '#4267B2'
                    },
                    backgroundOptions: {
                        color: '#FFFFFF'
                    }
                }
            },
            {
                id: 'test2',
                name: 'Cantos Personalizados',
                options: {
                    data: 'Teste de Cantos',
                    width: 200,
                    height: 200,
                    dotsOptions: {
                        type: 'square',
                        color: '#000000'
                    },
                    cornersSquareOptions: {
                        type: 'extra-rounded',
                        color: '#FF0000'
                    },
                    cornersDotOptions: {
                        type: 'dot',
                        color: '#00FF00'
                    },
                    backgroundOptions: {
                        color: '#FFFFFF'
                    }
                }
            },
            {
                id: 'test3',
                name: 'Gradiente Linear',
                options: {
                    data: 'Gradiente Linear',
                    width: 200,
                    height: 200,
                    dotsOptions: {
                        type: 'dots',
                        gradient: {
                            type: 'linear',
                            rotation: 0.7853981633974483, // 45 degrees
                            colorStops: [
                                { offset: 0, color: '#FF6B6B' },
                                { offset: 1, color: '#4ECDC4' }
                            ]
                        }
                    },
                    backgroundOptions: {
                        color: '#FFFFFF'
                    }
                }
            },
            {
                id: 'test4',
                name: 'Gradiente Radial',
                options: {
                    data: 'Gradiente Radial',
                    width: 200,
                    height: 200,
                    dotsOptions: {
                        type: 'square',
                        gradient: {
                            type: 'radial',
                            colorStops: [
                                { offset: 0, color: '#667eea' },
                                { offset: 1, color: '#764ba2' }
                            ]
                        }
                    },
                    backgroundOptions: {
                        color: '#FFFFFF'
                    }
                }
            },
            {
                id: 'test5',
                name: 'Estilo Classy',
                options: {
                    data: 'Estilo Classy',
                    width: 200,
                    height: 200,
                    dotsOptions: {
                        type: 'classy',
                        color: '#2c3e50'
                    },
                    cornersSquareOptions: {
                        type: 'square',
                        color: '#34495e'
                    },
                    backgroundOptions: {
                        color: '#ecf0f1'
                    }
                }
            },
            {
                id: 'test6',
                name: 'Formato SVG',
                options: {
                    data: 'Teste SVG',
                    width: 200,
                    height: 200,
                    format: 'svg',
                    type: 'svg',
                    dotsOptions: {
                        type: 'dots',
                        color: '#e74c3c'
                    },
                    backgroundOptions: {
                        color: '#FFFFFF'
                    }
                }
            }
        ];

        async function runTest(test) {
            const displayEl = document.getElementById(`${test.id}-display`);
            const statusEl = document.getElementById(`${test.id}-status`);
            
            displayEl.innerHTML = '<span style="color: #666;">Gerando...</span>';
            statusEl.className = 'status loading';
            statusEl.textContent = '⏳ Processando...';

            try {
                const format = test.options.format || 'png';
                const endpoint = format === 'svg' ? 
                    `${apiUrl}/qrcode/download?format=svg` : 
                    `${apiUrl}/qrcode/download?format=png`;

                const response = await fetch(endpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(test.options)
                });

                if (response.ok) {
                    const blob = await response.blob();
                    
                    if (format === 'svg') {
                        const text = await blob.text();
                        displayEl.innerHTML = text;
                    } else {
                        const imageUrl = URL.createObjectURL(blob);
                        displayEl.innerHTML = `<img src="${imageUrl}" alt="${test.name}">`;
                    }
                    
                    statusEl.className = 'status success';
                    statusEl.textContent = `✅ Sucesso (${blob.size} bytes)`;
                } else {
                    const error = await response.text();
                    statusEl.className = 'status error';
                    statusEl.textContent = `❌ Erro: ${response.status}`;
                    displayEl.innerHTML = '<span style="color: #e74c3c;">Erro ao gerar</span>';
                }
            } catch (error) {
                statusEl.className = 'status error';
                statusEl.textContent = `❌ Erro: ${error.message}`;
                displayEl.innerHTML = '<span style="color: #e74c3c;">Erro de conexão</span>';
            }
        }

        async function runAllTests() {
            for (const test of tests) {
                await runTest(test);
                // Pequeno delay entre os testes
                await new Promise(resolve => setTimeout(resolve, 500));
            }
        }

        function clearTests() {
            tests.forEach(test => {
                document.getElementById(`${test.id}-display`).innerHTML = '<span style="color: #999;">Aguardando...</span>';
                document.getElementById(`${test.id}-status`).textContent = '';
                document.getElementById(`${test.id}-status`).className = 'status';
            });
        }

        // Auto-executar testes ao carregar a página
        window.addEventListener('load', () => {
            setTimeout(runAllTests, 1000);
        });
    </script>
</body>
</html>
